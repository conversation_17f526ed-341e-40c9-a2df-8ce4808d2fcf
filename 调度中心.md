## 调度中心（Quartz）代码总览与开发参考

下面是一份对“调度中心”相关代码的系统化、可落地的开发参考文档，涵盖架构、运行机制、数据模型、API、扩展点与常见问题。核心代码集中于 sdata-ops-indicator 模块内的 Quartz 调度实现，另有 sdata-ops-system 模块下的 Spring @Scheduled 任务（任务中心）作为补充与对比。

---

### 1. 架构总览

- 调度内核：Quartz（注入 org.quartz.Scheduler，由 Spring Boot 自动装配）
- 核心职责划分
  - 控制层：SchedulerController 暴露任务管理 REST API（增/改/启/停/删/跑一次/日志）
  - 服务层：OpsQuartzJobService[Impl] 任务落库+注册到 Quartz
  - 执行层：AbstractQuartzJob + QuartzJobExecution/QuartzDisallowConcurrentExecution 统一切面式记录日志+转发业务执行
  - 执行业务分发：JobInvokeUtil 基于 jobType 路由到不同执行路径（反射方法/工作流/监控同步/告警）
  - 工具层：ScheduleUtils 封装 Quartz API；CronUtils 生成/校验 Cron；ScheduleConstants 常量
  - 持久化：OpsQuartzJob（任务表）、OpsQuartzJobLog（执行日志表）+ Mapper XML

- 配置存储：
  - application.yml 配置了 Quartz JDBC 存储（表前缀 QRTZ_..., 线程池 5）
  - QuartzConfig 类存在但默认注释掉，使用 Spring Boot Quartz autoconfig

- 与任务中心的关系：
  - 任务中心（TaskJob，@Scheduled）用于“运维任务清单”的周期性业务编排（生成/检查等），不是 Quartz；
  - 调度中心（Quartz）用于指标/工作流/监控的可视化配置型任务调度（增删改查、并发策略、快速/自定义 Cron）。

---

### 2. 关键模块与职责

- 控制器
  - sdata-ops-app/sdata-ops-indicator/.../controller/SchedulerController
  - 提供分页、添加、修改、启动、暂停、删除、执行一次、查询日志、工作流日志查询等端点
- 服务
  - OpsQuartzJobService/Impl：对 ops_quartz_job 的增改删 + 同步注册到 Quartz
- Quartz 工具
  - ScheduleUtils：构建 JobDetail / Trigger，处理 Misfire 策略，选择并发/非并发 Job 类
  - CronUtils：Cron 表达式校验/下次时间计算/“快捷模式”表达式生成
- Job 执行与记录
  - AbstractQuartzJob：统一生成 executionId、前置/后置、异常捕获、写 ops_quartz_job_log
  - QuartzJobExecution：允许并发
  - QuartzDisallowConcurrentExecution：禁止并发（@DisallowConcurrentExecution）
- 执行业务分发
  - JobInvokeUtil：基于 jobType 路由，支持反射方法执行、工作流执行、监控单元取数/告警；支持交易日控制（market）

---

### 3. 数据模型

- OpsQuartzJob（ops_quartz_job）任务主表（简要字段）
  - id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy, concurrent, status
  - jobType（dynamicTask/flowTask/warnSync/warnAlert）
  - bindWfId（工作流绑定）
  - warnId（监控中心简单单元ID）
  - market（日历市场，"0"表示不限；非"0"则按交易日校验）
  - timeType（quick/custom），frequency/startTime/endTime（用于 quick 模式生成 cron）
- OpsQuartzJobLog（ops_quartz_job_log）执行日志表
  - jobName, jobGroup, invokeTarget, jobMessage, status（0成功/1失败）
  - exceptionInfo, startTime, stopTime, jobId, executionId

代码摘录（实体）：
````java path=sdata-ops-base/sdata-ops-indicator-model/src/main/java/sdata/ops/base/indicator/model/entity/OpsQuartzJob.java mode=EXCERPT
@Data
@TableName("ops_quartz_job")
public class OpsQuartzJob implements Serializable {
  @TableId(type = IdType.AUTO) private Long id;
  private String jobName; private String jobGroup; private String invokeTarget;
  private String cronExpression; private String misfirePolicy; private String concurrent;
  private String status; private String bindWfId; private String jobType; private String warnId;
  private String market; private String timeType; private String frequency;
  private String startTime; private String endTime;
}
````

---

### 4. 运行时流程（生命周期）

- 新增任务
  1) Controller 接收 OpsQuartzJob，Service 设置默认值并校验 Cron
  2) 落库成功后调用 ScheduleUtils.createScheduleJob 注册 Quartz 任务（必要时先删除旧的）
  3) 新增默认“暂停”状态；默认“禁止并发”（concurrent="1"）

````java path=sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/service/impl/OpsQuartzJobServiceImpl.java mode=EXCERPT
job.setStatus(ScheduleConstants.Status.PAUSE.getValue());
job.setConcurrent("1");
job.setCronExpression(createCronExpression(job));
if (!CronUtils.isValid(job.getCronExpression())) throw new RuntimeException(...);
if (this.save(job)) { ScheduleUtils.createScheduleJob(scheduler, job); }
````

- 修改任务
  - 更新数据库后，删除同名 JobKey 并重建 Quartz 任务（避免脏状态）

- 启动/暂停
  - 更新数据库状态并调用 scheduler.resumeJob / pauseJob

- 删除
  - 先删数据库，再 scheduler.deleteJob

- 手动执行一次
  - scheduler.triggerJob(jobKey, dataMap)；dataMap 放入 TASK_PROPERTIES

- Quartz 触发执行
  - AbstractQuartzJob.execute: 生成 executionId + 前置标记时间 → doExecute → 写日志（成功/异常）

````java path=sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/handler/quartz/job/AbstractQuartzJob.java mode=EXCERPT
String executionId = String.valueOf(IdWorker.getId());
context.getJobDetail().getJobDataMap().put(ScheduleConstants.EXECUTION_ID, executionId);
BeanUtils.copyProperties(context.getMergedJobDataMap().get(TASK_PROPERTIES), sysJob);
try { before(...); doExecute(...); after(..., null); }
catch (Exception e) { after(..., e); }
````

- 并发与非并发
  - 根据 OpsQuartzJob.concurrent 值选择 Job 类
    - "0" → 允许并发 → QuartzJobExecution
    - "1" → 禁止并发 → QuartzDisallowConcurrentExecution（带注解）

````java path=sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/utils/ScheduleUtils.java mode=EXCERPT
private static Class<? extends Job> getQuartzJobClass(OpsQuartzJob job) {
  boolean isConcurrent = "0".equals(job.getConcurrent());
  return isConcurrent ? QuartzJobExecution.class : QuartzDisallowConcurrentExecution.class;
}
````

---

### 5. 任务执行分发（jobType）

- jobType 决定执行路径，集中在 JobInvokeUtil.invokeMethod
  - dynamicTask：invokeTarget 反射调用（支持字符串/数值/布尔参数）
  - flowTask：调用 WorkFlowExecHandler.invokeWorkflowExecution(bindWfId, ...)
  - warnSync：OpsWarnInfoService.syncData(warnId)
  - warnAlert：OpsWarnInfoService.alert(warnId)
- 交易日控制（market）
  - market != "0" 时，会通过 SystemFeignService.isTradeDay(LocalDate, market) 非交易日跳过执行

代码摘录：
````java path=sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/handler/quartz/job/JobInvokeUtil.java mode=EXCERPT
if (!"0".equals(sysJob.getMarket())) {
  var sys = SpringBeanUtil.getBean(SystemFeignService.class);
  if (!sys.isTradeDay(LocalDate.now(), sysJob.getMarket())) { log.info("非交易日"); return; }
}
switch (jobType) {
  case "dynamicTask": invokeRefMethod(sysJob); break;
  case "flowTask": SpringBeanUtil.getBean(WorkFlowExecHandler.class)
      .invokeWorkflowExecution(sysJob.getBindWfId(), null, String.valueOf(sysJob.getId()), "schedule", executionId); break;
  case ScheduleConstants.SCH_TYPE_WARN_SYNC: SpringBeanUtil.getBean(OpsWarnInfoService.class).syncData(sysJob.getWarnId()); break;
  case ScheduleConstants.SCH_TYPE_WARN_ALERT: SpringBeanUtil.getBean(OpsWarnInfoService.class).alert(sysJob.getWarnId()); break;
}
````

---

### 6. Cron 与时间策略

- 自定义模式：timeType="custom"，直接使用 cronExpression
- 快捷模式：timeType="quick"，由 frequency + startTime + endTime 自动生成 Cron
  - 频率支持："5分钟" / "10分钟" / "30分钟" / "1小时"
  - 采用“分段小时范围”的 Cron 格式

````java path=sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/utils/CronUtils.java mode=EXCERPT
public static String generateCron(String frequency, String startTime, String endTime) {
  switch (frequency) {
    case "5分钟":  return String.format("%d %d/5 %d-%d * * ? ",  ss, sm, sh, eh);
    case "10分钟": return String.format("%d %d/10 %d-%d * * ? ", ss, sm, sh, eh);
    case "30分钟": return String.format("%d %d/30 %d-%d * * ? ", ss, sm, sh, eh);
    case "1小时":  return String.format("%d %d %d-%d * * ? ",   ss, sm, sh, eh);
  }
}
````

- Misfire 策略（ScheduleConstants.MISFIRE_*）
  - 0 默认
  - 1 忽略错过立即补
  - 2 触发一次后按计划
  - 3 不触发立即执行

````java path=sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/utils/ScheduleUtils.java mode=EXCERPT
switch (job.getMisfirePolicy()) {
  case MISFIRE_IGNORE_MISFIRES: return cb.withMisfireHandlingInstructionIgnoreMisfires();
  case MISFIRE_FIRE_AND_PROCEED: return cb.withMisfireHandlingInstructionFireAndProceed();
  case MISFIRE_DO_NOTHING:       return cb.withMisfireHandlingInstructionDoNothing();
  default: return cb;
}
````

---

### 7. API 设计（控制器端点）

Base Path: /indicator/scheduler

- GET /page?jobName&status&page=1&pageSize=10
- POST /add  Body: OpsQuartzJob
- POST /edit Body: OpsQuartzJob
- GET /resume?id=...
- GET /pause?id=...
- GET /delete?id=...
- GET /run?id=...
- GET /log?id=...&pageNo=1&pageSize=10
- GET /log/wfLog?executionId=...

代码摘录：
````java path=sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/controller/SchedulerController.java mode=EXCERPT
@PostMapping("/add")
public R<Object> add(@Validated(SaveGroup.class) @RequestBody OpsQuartzJob job)
  throws SchedulerException, TaskException {
  opsQuartzJobService.addJob(job); return R.success("添加成功");
}
````

请求示例（新增一个“禁止并发”的反射任务，交易日受限）：
````json mode=EXCERPT
{
  "jobName": "统计日增量",
  "jobGroup": "DEFAULT",
  "jobType": "dynamicTask",
  "invokeTarget": "sampleBean.refreshDaily('FOO', 1, true)",
  "timeType": "quick",
  "frequency": "30分钟",
  "startTime": "08:00:00",
  "endTime": "20:00:00",
  "misfirePolicy": "1",
  "concurrent": "1",
  "market": "SSE"
}
````

---

### 8. 配置与部署要点

- Quartz 使用 JDBC Store
  - spring.quartz.job-store-type=jdbc
  - 表前缀 QRTZ_（需初始化 Quartz 表）
  - 线程数 5
- Standalone 启动配置见 application.yml（sdata-ops-standalone 模块）
- QuartzConfig 默认注释，采用 Spring Boot 自动配置（若自定义 SchedulerFactoryBean，需小心和 Boot 自动配置的交互）

````yaml path=sdata-ops-standalone/src/main/resources/application.yml mode=EXCERPT
spring:
  quartz:
    job-store-type: jdbc
    properties:
      org:
        quartz:
          threadPool:
            threadCount: 5
          scheduler:
            instanceName: taskScheduler
          jobStore:
            tablePrefix: QRTZ_
````

---

### 9. 扩展指南

- 新增 jobType
  - 在 JobInvokeUtil.switch 中增加分支，封装具体业务调用
  - 为该类型设计必要字段（如需要）并扩展 OpsQuartzJob/表结构与 Mapper
- 新增反射型任务
  - jobType="dynamicTask"，invokeTarget="beanName.method('str', 1, true, 2L, 3D)"
  - 确保 beanName 可从 Spring 容器获取（或使用全类名反射）
- 新增工作流任务
  - jobType="flowTask"，bindWfId 绑定工作流；由 WorkFlowExecHandler 执行
- 监控中心任务
  - warnSync（取数）、warnAlert（告警），绑定 warnId 与 jobGroup=ScheduleConstants.GROUP_WARN_CENTER

---

### 10. 审计与日志

- 每次执行都会记录 OpsQuartzJobLog，包含 executionId，可用于关联工作流日志
- 错误信息截断至 2000 字符，状态 1
- 日志查询：/indicator/scheduler/log，工作流日志：/indicator/scheduler/log/wfLog

````java path=sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/handler/quartz/job/AbstractQuartzJob.java mode=EXCERPT
sysJobLog.setExecutionId((String) context.getJobDetail().getJobDataMap().get(ScheduleConstants.EXECUTION_ID));
SpringBeanUtil.getBean(OpsQuartzJobLogService.class).save(sysJobLog);
````

---

### 11. 常见问题与建议

- 并发标志与默认值
  - 服务新增默认 concurrent="1"（禁止并发），如需允许并发需显示设置 "0"
- Cron 快捷模式仅支持固定频率+时间段，复杂场景请使用 custom
- 交易日控制
  - market != "0" 时，非交易日将直接跳过任务执行
- Misfire 策略需与业务 SLA 匹配（忽略/触发一次/不触发）
- 任务组（jobGroup）
  - 监控中心建议使用 ScheduleConstants.GROUP_WARN_CENTER 进行隔离/筛选
- 调度一致性
  - 更新任务会先删除后重建，避免旧 Trigger 残留
- Quartz 表与连接池
  - 确保 QRTZ_* 表初始化；线程池大小与任务负载相匹配

---

### 12. 与“任务中心”（TaskJob）的关系

- TaskJob（@Scheduled）负责“任务清单”的业务编排，与 Quartz 调度中心互补
- 任务中心文档参考仓库已有：
  - 任务中心模块-功能与流程说明.md
  - TaskJob功能分析文档.md

---

### 13. 开发者速查（关键代码）

- 选择并发/非并发 Job 类
````java path=sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/utils/ScheduleUtils.java mode=EXCERPT
return "0".equals(job.getConcurrent()) ? QuartzJobExecution.class : QuartzDisallowConcurrentExecution.class;
````

- 任务执行分发
````java path=sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/handler/quartz/job/JobInvokeUtil.java mode=EXCERPT
switch (jobType) { case "dynamicTask": ...; case "flowTask": ...; case "warnSync": ...; case "warnAlert": ...; }
````

- Quartz 注册入口
````java path=sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/utils/ScheduleUtils.java mode=EXCERPT
JobDetail jd = JobBuilder.newJob(jobClass).withIdentity(getJobKey(jobId, group)).build();
CronTrigger tg = TriggerBuilder.newTrigger().withIdentity(getTriggerKey(jobId, group)).withSchedule(cb).build();
scheduler.scheduleJob(jd, tg);
````

- 执行日志落库
````java path=sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/handler/quartz/job/AbstractQuartzJob.java mode=EXCERPT
if (e != null) { sysJobLog.setStatus("1"); sysJobLog.setExceptionInfo(errorMsg); } else { sysJobLog.setStatus("0"); }
````

---

### 14. 验证与排障建议

- 新增任务后检查：
  - ops_quartz_job 是否入库、Quartz 日志是否启动时加载
  - /indicator/scheduler/run?id=... 能否触发一次成功
  - ops_quartz_job_log 是否新增记录
- 定位不执行：
  - Cron 是否有效（CronUtils.isValid）
  - NextExecution 是否非空（ScheduleUtils 中判断）
  - market 是否为非交易日
  - misfire 策略是否导致预期外行为
  - jobGroup/jobKey 是否一致，是否被 pause
- 监控中心任务：
  - warnId 存在、服务 OpsWarnInfoService 可用
- 工作流任务：
  - bindWfId 有效，WorkFlowExecHandler 可用

---
