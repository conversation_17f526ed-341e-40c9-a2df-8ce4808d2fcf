package sdata.ops.flow.controller;


import sdata.ops.base.flow.model.bo.WfTaskBo;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.flow.service.IWfInstanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import sdata.ops.common.api.R;

/**
 * 工作流流程实例管理
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/bpm/instance")
public class WfInstanceController {

    private final IWfInstanceService instanceService;

    /**
     * 激活或挂起流程实例
     *
     * @param state      1:激活,2:挂起
     * @param instanceId 流程实例ID
     */
    @ControllerAuditLog(value = "流程实例-激活/挂起", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/updateState")
    public R<Object> updateState(@RequestParam Integer state, @RequestParam String instanceId) {
        instanceService.updateState(state, instanceId);
        return R.success("");
    }

    /**
     * 结束流程实例
     *
     * @param bo 流程任务业务对象
     */
    @ControllerAuditLog(value = "流程实例-结束", operateType = OperateType.UPDATE, moduleName = ModuleName.PROCESS)
    @PostMapping(value = "/stopProcessInstance")
    public R<Object> stopProcessInstance(@RequestBody WfTaskBo bo) {
        instanceService.stopProcessInstance(bo);
        return R.success("");
    }

    /**
     * 删除流程实例
     *
     * @param instanceId   流程实例ID
     * @param deleteReason 删除原因
     */
    @ControllerAuditLog(value = "流程实例-删除", operateType = OperateType.DELETE, moduleName = ModuleName.PROCESS)
    @Deprecated
    @DeleteMapping(value = "/delete")
    public R<Object> delete(@RequestParam String instanceId, String deleteReason) {
        instanceService.delete(instanceId, deleteReason);
        return R.success("");
    }

    /**
     * 查询流程实例详情信息
     *
     * @param procInsId 流程实例ID
     * @param deployId  流程部署ID
     */
    @ControllerAuditLog(value = "流程实例-详情", operateType = OperateType.QUERY, moduleName = ModuleName.PROCESS)
    @GetMapping("/detail")
    public R<Object> detail(String procInsId, String deployId) {
        return R.data(instanceService.queryDetailProcess(procInsId, deployId));
    }
}
