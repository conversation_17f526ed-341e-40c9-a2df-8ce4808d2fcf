package sdata.ops.flow.handler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import sdata.ops.base.flow.model.entity.OpsAiWorkflow;
import sdata.ops.base.flow.model.vo.FlowTestVO;
import sdata.ops.base.flow.model.vo.OpsAiWorkflowVO;
import sdata.ops.common.config.feign.FeignLocal;
import sdata.ops.flow.api.feign.WorkFlowFeignService;
import sdata.ops.flow.service.OpsWorkflowService;

@Component
@FeignLocal(WorkFlowFeignService.class)
@RequiredArgsConstructor
public class WorkFlowLocalHandler implements WorkFlowFeignService {


    private final OpsWorkflowService workflowService;
    @Override
    public OpsAiWorkflow getFlowById(String id) {
        return workflowService.getById(id);
    }
}
