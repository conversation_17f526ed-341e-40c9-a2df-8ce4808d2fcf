package sdata.ops.flow.service;

import sdata.ops.base.flow.model.bo.PageQuery;
import sdata.ops.base.flow.model.bo.TableDataInfo;
import sdata.ops.base.flow.model.bo.WfCopyBo;
import sdata.ops.base.flow.model.bo.WfTaskBo;
import sdata.ops.base.flow.model.vo.WfCopyVo;

import java.util.List;

/**
 * 流程抄送Service接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface IWfCopyService {

    /**
     * 查询流程抄送
     *
     * @param copyId 流程抄送主键
     * @return 流程抄送
     */
    WfCopyVo queryById(Long copyId);

    /**
     * 查询流程抄送列表
     *
     * @param wfCopy 流程抄送
     * @return 流程抄送集合
     */
    TableDataInfo<WfCopyVo> selectPageList(WfCopyBo wfCopy, PageQuery pageQuery);

    /**
     * 查询流程抄送列表
     *
     * @param wfCopy 流程抄送
     * @return 流程抄送集合
     */
    List<WfCopyVo> selectList(WfCopyBo wfCopy);

    /**
     * 抄送
     * @param taskBo
     * @return
     */
    boolean makeCopy(WfTaskBo taskBo);

    TableDataInfo<WfCopyVo> selectPageListByPerId(WfCopyBo copyBo, PageQuery pageQuery);
}
