package sdata.ops.system.handler;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.dto.OpsTaskInfoUpdateDTO;
import sdata.ops.base.system.model.entity.*;
import sdata.ops.base.system.model.vo.SystemTestVO;
import sdata.ops.base.system.model.vo.WorkDayVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.config.feign.FeignLocal;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.system.api.feign.SystemFeignService;
import sdata.ops.system.service.*;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
@FeignLocal(SystemFeignService.class)
@RequiredArgsConstructor
public class SystemFeignLocalHandler implements SystemFeignService {

    private final SystemUserService systemUserService;

    private final OpsTaskFundInfoService fundInfoService;

    private final OpsTradeTypeService tradeTypeService;

    private final OpsSysDataPermService opsSysDataPermService;

    private final OpsSysDictItemService sysDictItemService;

    @Override
    public SystemTestVO getTestVo() {
        return null;
    }

    @Override
    public SystemTestVO getTransVo() {
        return null;
    }

    @Override
    public Map<String, String> idNameMapper() {
        return systemUserService.findNameIdMapping();
    }

    @Override
    public R<String> saveFundInfo(OpsTaskInfoUpdateDTO dto) {
        fundInfoService.saveFromIndicator(dto, "system");
        return R.success("完成");
    }

    @Override
    public List<OpsTaskFundInfo> queryFundInfos(String replicaId) {
        return fundInfoService.list(Wrappers.lambdaQuery(OpsTaskFundInfo.class)
                .eq(OpsTaskFundInfo::getTaskReplicaId, replicaId).
                eq(OpsTaskFundInfo::getBizDate, DateUtil.format(new Date(), "yyyyMMdd")));
    }

    @Override
    public R<Boolean> checkTodayIsWorkDay(String date) {
        return R.data(tradeTypeService.checkWorkdayByToday());
    }

    @Override
    public R<String> getLastWorkDay(String date) {
        return R.data(tradeTypeService.getLastWorkDay(date));
    }

    @Override
    public R<Boolean> deleteTaImportDate(String date) {
        LambdaQueryWrapper<OpsTaskFundInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskFundInfo::getDataId, -200);
        queryWrapper.eq(OpsTaskFundInfo::getBizDate, date);
        fundInfoService.remove(queryWrapper);
        return R.success("");
    }

    @Override
    public R<Boolean> deleteFundInfoImportByDateAndDataId(String date, String dataId) {
        LambdaQueryWrapper<OpsTaskFundInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskFundInfo::getDataId, dataId);
        queryWrapper.eq(OpsTaskFundInfo::getBizDate, date);
        fundInfoService.remove(queryWrapper);
        return R.success("");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> saveAllFund(List<OpsTaskFundInfo> arr) {
        fundInfoService.saveBatch(arr);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    @Override
    public R<String> getNextWorkDay(String today) {
        return R.data(tradeTypeService.getNextWorkDay(today));
    }

    @Override
    public String getWorkDay(WorkDayVO vo) {
        if (vo.getOffset() > 0) {
            return tradeTypeService.getNextWorkDayOffset(vo.getDay(), vo.getOffset());
        }
        return tradeTypeService.getLastWorkDayOffset(vo.getDay(), Math.abs(vo.getOffset()));
    }

    @Override
    public R<Object> getById(String id) {
        return R.data(opsSysDataPermService.getById(id));
    }

    @Override
    public JSONObject getDataPermById(String id, String userId) {
        return opsSysDataPermService.getDataPermById(id, userId);
    }

    @Override
    public R<List<OpsSysDictItem>> dictListByTypes(List<String> dictType) {
        List<OpsSysDictItem> list = sysDictItemService.lambdaQuery()
                .in(CollUtil.isNotEmpty(dictType), OpsSysDictItem::getDictType, dictType)
                .list();
        return R.data(list);
    }

    @Override
    public String selectDeptById(Long deptId) {
        // TODO
        return "";
    }

    @Override
    public String selectRoleById(Long roleId) {
        // TODO
        return "";
    }

    @Override
    public String selectNickNameById(Long userId) {
        // TODO
        return "";
    }

    @Override
    public List<String> findSameDeptUserIds(String userId) {
        // TODO
        return List.of();
    }

    @Override
    public List<SystemUser> findAllByPerUser(String deptId) {
        // TODO
        return List.of();
    }

    @Override
    public List<OpsSysRole> selectMyRoleList(String userId) {
        // TODO
        return List.of();
    }

    @Override
    public List<OpsSysOrg> selectMyOrgList(String userId) {
        // TODO
        return List.of();
    }

    @Override
    public List<OpsSysOrg> selectChildrenDeptById(String dept) {
        // TODO
        return List.of();
    }

    @Override
    public Boolean isTradeDay(LocalDate date, String market) {
        return SpringBeanUtil.getBean(OpsSysCalendarService.class).isTradeDay(date, market);
    }
}
