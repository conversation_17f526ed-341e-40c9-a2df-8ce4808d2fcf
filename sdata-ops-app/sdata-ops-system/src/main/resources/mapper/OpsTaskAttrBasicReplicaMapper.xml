<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sdata.ops.system.mapper.OpsTaskAttrBasicReplicaMapper">

    <resultMap id="BaseResultMap" type="sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="taskNo" column="task_no" jdbcType="VARCHAR"/>
        <result property="taskStatus" column="task_status" jdbcType="TINYINT"/>
        <result property="taskName" column="task_name" jdbcType="VARCHAR"/>
        <result property="taskType" column="task_type" jdbcType="VARCHAR"/>
        <result property="taskTriggerType" column="task_trigger_type" jdbcType="VARCHAR"/>
        <result property="taskTriggerId" column="task_trigger_id" jdbcType="VARCHAR"/>
        <result property="taskCronVal" column="task_cron_val" jdbcType="VARCHAR"/>
        <result property="taskDesc" column="task_desc" jdbcType="VARCHAR"/>
        <result property="taskCompleteType" column="task_complete_type" jdbcType="VARCHAR"/>
        <result property="taskCompleteUnitId" column="task_complete_unit_id" jdbcType="VARCHAR"/>
        <result property="taskAuditType" column="task_audit_type" jdbcType="CHAR"/>
        <result property="taskAuditUnitId" column="task_audit_unit_id" jdbcType="VARCHAR"/>
        <result property="taskWarnNotice" column="task_warn_notice" jdbcType="VARCHAR"/>
        <result property="taskPriority" column="task_priority" jdbcType="CHAR"/>
        <result property="taskLevel" column="task_level" jdbcType="CHAR"/>
        <result property="taskAttachmentsType" column="task_attachments_type" jdbcType="CHAR"/>
        <result property="taskOwnerType" column="task_owner_type" jdbcType="CHAR"/>
        <result property="taskOwnerId" column="task_owner_id" jdbcType="VARCHAR"/>
        <result property="taskOwnerVal" column="task_owner_val" jdbcType="VARCHAR"/>
        <result property="taskCheckReq" column="task_check_req" jdbcType="CHAR"/>
        <result property="taskCheckType" column="task_check_type" jdbcType="CHAR"/>
        <result property="taskCheckId" column="task_check_id" jdbcType="VARCHAR"/>
        <result property="taskCheckVal" column="task_check_val" jdbcType="VARCHAR"/>
        <result property="taskStartTime" column="task_start_time" jdbcType="TIMESTAMP"/>
        <result property="taskEndTime" column="task_end_time" jdbcType="TIMESTAMP"/>
        <result property="taskTags" column="task_tags" jdbcType="VARCHAR"/>
        <result property="taskAuthType" column="task_auth_type" jdbcType="CHAR"/>
        <result property="taskAuthId" column="task_auth_id" jdbcType="VARCHAR"/>
        <result property="taskAuthVal" column="task_auth_val" jdbcType="VARCHAR"/>
        <result property="taskAuthScope" column="task_auth_scope" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="taskDeferredType" column="task_deferred_type" jdbcType="CHAR"/>
        <result property="taskDeferredCount" column="task_deferred_count" jdbcType="INTEGER"/>
        <result property="dependOnIds" column="depend_on_ids" jdbcType="VARCHAR"/>
        <result property="requiredItem" column="required_item" jdbcType="VARCHAR"/>
        <result property="taskRefId" column="task_ref_id" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="fundResultMap" type="java.util.Map">
        <result column="num" property="num" jdbcType="BIGINT"></result>
        <result column="owner_org_id" property="ownerOrgId" jdbcType="VARCHAR"></result>
        <result column="complete_status" property="completeStatus" jdbcType="VARCHAR"></result>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,parent_id,task_no,
        task_status,task_name,task_type,
        task_trigger_type,task_trigger_id,task_cron_val,
        task_desc,task_complete_type,task_complete_unit_id,
        task_audit_type,task_audit_unit_id,task_warn_notice,
        task_priority,task_level,task_attachments_type,
        task_owner_type,task_owner_id,task_owner_val,
        task_check_req,task_check_type,task_check_id,
        task_check_val,task_start_time,task_end_time,
        task_tags,task_auth_type,task_auth_id,
        task_auth_val,task_auth_scope,create_time,
        create_by,update_time,update_by,
        deleted,task_deferred_type,task_deferred_count,
        depend_on_ids,required_item,task_ref_id,owner_org_id,
          check_org_id,task_start_threshold,task_end_threshold,task_bind_template_id,task_sort,access_level,work_amount,work_amount_flag,
          import_status, task_name_append ,task_append_type,task_create_type
    </sql>
    <insert id="insertScriptTempTable">
        insert into ops_script_temp
        values (#{id}, #{inid}, #{dataid}, #{nowval}, #{time})
    </insert>
    <select id="viewListByTemplateId" resultType="sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        ops_task_attr_basic_replica a
        WHERE
        a.id IN (
        SELECT
        b.task_replica_id
        FROM
        ops_task_template_relation b
        WHERE
        b.template_id = #{id}
        )
    </select>
    <select id="queryListByTemplateId" resultType="sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica">
        select
        <include refid="Base_Column_List"></include>
        from OPS_TASK_ATTR_BASIC_REPLICA where id in (select TASK_REPLICA_ID from OPS_TASK_TEMPLATE_RELATION where
        TEMPLATE_ID =#{id}) and task_create_type=0
    </select>

    <select id="queryListByImportStatus" resultType="sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica">
        select * from (select c.*
        from ops_task_template a
                 left join ops_task_template_relation b on a.id = b.template_id
                 left join ops_task_attr_basic_replica c on b.task_replica_id = c.id
        where c.import_status = 1
          and owner_org_id = #{orgId}) as fns order by fns.task_sort,task_no
    </select>

    <select id="queryListByImportStatusByPriority" resultType="sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica">
        select * from (select c.*
                       from ops_task_template a
                                left join ops_task_template_relation b on a.id = b.template_id
                                left join ops_task_attr_basic_replica c on b.task_replica_id = c.id
                       where c.import_status = 1 and TASK_PRIORITY=1
                         and owner_org_id = #{orgId}) as fns order by fns.task_sort,task_no
    </select>
    <select id="queryUnProcessDataByUserId" resultType="sdata.ops.base.indicator.model.entity.OpsSpecThirdInfo">
        with c1 as
                 (SELECT *
                  FROM ops_spec_third_info
                  where USER_ID = #{userId}
                 )
                ,
             c2 as
                 (select *
                  from ops_script_temp
                 )
        select c1.*
        from c1
                 left join c2
                           on c1.data_id = c2.third_id
        where c2.indicator_id is null
    </select>
    <select id="queryListHaveImportByTemplateId"
            resultType="sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica">

        select
        <include refid="Base_Column_List"></include>
        from ops_task_attr_basic_replica where id in (select task_replica_id from ops_task_template_relation where
        TEMPLATE_ID
        in
        <foreach collection="ls" item="a" separator="," close=")" open="(">
            #{a}
        </foreach>
        )
        and import_status=1

    </select>
    <select id="queryIndicatorByTriggerId" resultType="java.lang.String">
        select indicator_id
        from ops_trade_type
        where id = #{id}
    </select>
    <select id="querySingleThirdInfo" resultType="sdata.ops.base.indicator.model.entity.OpsSpecThirdInfo">
        SELECT *
        FROM ops_spec_third_info
        where data_id = #{dataId}
    </select>
    <select id="queryListByTemplateIdAndUserIdWithOwner"
            resultType="sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica">
        select
        <include refid="Base_Column_List"></include>
        from ops_task_attr_basic_replica where id in
        (select task_replica_id from OPS_TASK_TEMPLATE_RELATION where TEMPLATE_ID
        in <foreach collection="ids" open="(" close=")" separator="," item="k">
        #{k}
    </foreach>                                                       )
        and ((task_owner_type=2 and task_owner_id=#{userId})or (task_check_type=2 and task_check_id=#{userId}))

    </select>

    <select id="getAllFundCount" resultMap="fundResultMap">
        select IFNULL(count(*), 0) num, t2.owner_org_id owner_org_id, t1.complete_status complete_status
        from ops_task_attr_basic_replica t2
                 left join ops_task_fund_info t1 on t1.task_replica_id = t2.id and t2.import_status = 1
        where year (t1.end_date) = #{year}
          and month (t1.end_date) = #{month}
        group by t2.owner_org_id, t1.complete_status
    </select>
    <select id="queryListByTemplateIdOfCreateType1"
            resultType="sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica">

        select
        <include refid="Base_Column_List"></include>
        from ops_task_attr_basic_replica
        where id in (select task_replica_id
        from ops_task_template_relation where template_id in (select id from ops_task_template where template_status=1)
        )
        and task_create_type=1
    </select>
    <select id="queryParentAndSelfListBySelfIds"
            resultType="sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica">
        select distinct r.*
        from ops_task_attr_basic_replica r
         start with r.id in
            <foreach collection="ids" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
        connect by prior r.parent_id = r.id
        order by r.id
    </select>
</mapper>
