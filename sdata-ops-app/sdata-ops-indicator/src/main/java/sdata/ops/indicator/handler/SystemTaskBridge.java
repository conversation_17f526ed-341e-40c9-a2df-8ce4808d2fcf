package sdata.ops.indicator.handler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import sdata.ops.common.api.R;
import sdata.ops.system.api.feign.SystemFeignService;

/**
 * 系统任务桥接Bean
 * 用于调度中心通过Feign调用任务中心的@Scheduled任务
 * 
 * 迁移说明：
 * - 原任务中心的@Scheduled任务通过此桥接Bean转为调度中心的Quartz任务
 * - 保持原有的工作日检查、分布式锁、执行频率等语义
 * - 使用Feign本地代理机制，避免HTTP调用开销
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SystemTaskBridge {

    private final SystemFeignService systemFeignService;

    /**
     * 每日任务清单生成
     * 原@Scheduled(cron = "0 0 1 * * ?")
     * 含分布式锁1001和工作日检查
     */
    public void creatTask() {
        log.info("SystemTaskBridge.creatTask() - 开始执行每日任务清单生成");
        try {
            R<String> result = systemFeignService.triggerCreatTask();
            if (result.isSuccess()) {
                log.info("SystemTaskBridge.creatTask() - 执行成功: {}", result.getData());
            } else {
                log.error("SystemTaskBridge.creatTask() - 执行失败: {}", result.getMessage());
                throw new RuntimeException("每日任务清单生成失败: " + result.getMessage());
            }
        } catch (Exception e) {
            log.error("SystemTaskBridge.creatTask() - 执行异常", e);
            throw e;
        }
    }

    /**
     * 三方系统数据抓取
     * 原@Scheduled(cron = "0 0 6-22 * * ?")
     * 含工作日检查
     */
    public void obtThirdSystemDataForSch() {
        log.info("SystemTaskBridge.obtThirdSystemDataForSch() - 开始执行三方系统数据抓取");
        try {
            R<String> result = systemFeignService.triggerObtThirdSystemDataForSch();
            if (result.isSuccess()) {
                log.info("SystemTaskBridge.obtThirdSystemDataForSch() - 执行成功: {}", result.getData());
            } else {
                log.error("SystemTaskBridge.obtThirdSystemDataForSch() - 执行失败: {}", result.getMsg());
                throw new RuntimeException("三方系统数据抓取失败: " + result.getMsg());
            }
        } catch (Exception e) {
            log.error("SystemTaskBridge.obtThirdSystemDataForSch() - 执行异常", e);
            throw e;
        }
    }

    /**
     * 任务可见性轮询更新
     * 原@Scheduled(cron = "0 0 6-22 * * ?")
     * 含工作日检查
     */
    public void updateTaskIsSearchForOaSystemOrMailServer() {
        log.info("SystemTaskBridge.updateTaskIsSearchForOaSystemOrMailServer() - 开始执行任务可见性轮询更新");
        try {
            R<String> result = systemFeignService.triggerUpdateTaskIsSearchForOaSystemOrMailServer();
            if (result.isSuccess()) {
                log.info("SystemTaskBridge.updateTaskIsSearchForOaSystemOrMailServer() - 执行成功: {}", result.getData());
            } else {
                log.error("SystemTaskBridge.updateTaskIsSearchForOaSystemOrMailServer() - 执行失败: {}", result.getMsg());
                throw new RuntimeException("任务可见性轮询更新失败: " + result.getMsg());
            }
        } catch (Exception e) {
            log.error("SystemTaskBridge.updateTaskIsSearchForOaSystemOrMailServer() - 执行异常", e);
            throw e;
        }
    }

    /**
     * 增量任务创建
     * 原@Scheduled(cron = "0 0 6-22 * * ?")
     * 含分布式锁1002和工作日检查
     */
    public void insertTaskForOaSystemOrMailServer() {
        log.info("SystemTaskBridge.insertTaskForOaSystemOrMailServer() - 开始执行增量任务创建");
        try {
            R<String> result = systemFeignService.triggerInsertTaskForOaSystemOrMailServer();
            if (result.isSuccess()) {
                log.info("SystemTaskBridge.insertTaskForOaSystemOrMailServer() - 执行成功: {}", result.getData());
            } else {
                log.error("SystemTaskBridge.insertTaskForOaSystemOrMailServer() - 执行失败: {}", result.getMsg());
                throw new RuntimeException("增量任务创建失败: " + result.getMsg());
            }
        } catch (Exception e) {
            log.error("SystemTaskBridge.insertTaskForOaSystemOrMailServer() - 执行异常", e);
            throw e;
        }
    }

    /**
     * 特殊明细任务更新
     * 原@Scheduled(cron = "0 0 6-22 * * ?")
     */
    public void updateSpecDetailTask() {
        log.info("SystemTaskBridge.updateSpecDetailTask() - 开始执行特殊明细任务更新");
        try {
            R<String> result = systemFeignService.triggerUpdateSpecDetailTask();
            if (result.isSuccess()) {
                log.info("SystemTaskBridge.updateSpecDetailTask() - 执行成功: {}", result.getData());
            } else {
                log.error("SystemTaskBridge.updateSpecDetailTask() - 执行失败: {}", result.getMsg());
                throw new RuntimeException("特殊明细任务更新失败: " + result.getMsg());
            }
        } catch (Exception e) {
            log.error("SystemTaskBridge.updateSpecDetailTask() - 执行异常", e);
            throw e;
        }
    }

    /**
     * 特殊时间类型脚本执行
     * 原@Scheduled(cron = "0 0 2 * * ?")
     * 含工作日检查
     */
    public void updateTaskOnlyOnce() {
        log.info("SystemTaskBridge.updateTaskOnlyOnce() - 开始执行特殊时间类型脚本");
        try {
            R<String> result = systemFeignService.triggerUpdateTaskOnlyOnce();
            if (result.isSuccess()) {
                log.info("SystemTaskBridge.updateTaskOnlyOnce() - 执行成功: {}", result.getData());
            } else {
                log.error("SystemTaskBridge.updateTaskOnlyOnce() - 执行失败: {}", result.getMsg());
                throw new RuntimeException("特殊时间类型脚本执行失败: " + result.getMsg());
            }
        } catch (Exception e) {
            log.error("SystemTaskBridge.updateTaskOnlyOnce() - 执行异常", e);
            throw e;
        }
    }

    /**
     * 延期任务状态扫描
     * 原@Scheduled(cron = "0 0 13,15,17,22 * * ?")
     */
    public void timerEverDayScannerDelayTaskStatus() {
        log.info("SystemTaskBridge.timerEverDayScannerDelayTaskStatus() - 开始执行延期任务状态扫描");
        try {
            R<String> result = systemFeignService.triggerTimerEverDayScannerDelayTaskStatus();
            if (result.isSuccess()) {
                log.info("SystemTaskBridge.timerEverDayScannerDelayTaskStatus() - 执行成功: {}", result.getData());
            } else {
                log.error("SystemTaskBridge.timerEverDayScannerDelayTaskStatus() - 执行失败: {}", result.getMsg());
                throw new RuntimeException("延期任务状态扫描失败: " + result.getMsg());
            }
        } catch (Exception e) {
            log.error("SystemTaskBridge.timerEverDayScannerDelayTaskStatus() - 执行异常", e);
            throw e;
        }
    }
}
