package sdata.ops.indicator.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import sdata.ops.base.indicator.model.dto.OpsMetricFieldVO;
import sdata.ops.base.indicator.model.entity.OpsMetricApiConfig;
import sdata.ops.base.indicator.model.entity.OpsMetricBasic;
import sdata.ops.base.indicator.model.entity.OpsMetricTag;
import sdata.ops.base.indicator.model.vo.OpsMetricApiConfigVO;
import sdata.ops.base.indicator.model.vo.OpsMetricBasicApiVO;
import sdata.ops.base.indicator.model.vo.OpsMetricBasicFieldVO;
import sdata.ops.base.indicator.model.vo.OpsMetricBasicVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.PageCustomController;
import sdata.ops.common.api.R;
import sdata.ops.common.core.util.JsonUtils;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.indicator.service.OpsMetricBasicService;
import sdata.ops.system.api.feign.SystemFeignService;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/metric-basic")
@RequiredArgsConstructor
public class MetricBasicController extends PageCustomController {


    private final OpsMetricBasicService metricBasicService;

    private final SystemFeignService systemFeignService;

    @ControllerAuditLog(value = "指标基本信息-分页查", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/page")
    public R<Object> page(@RequestParam(required = false) String name,
                          @RequestParam(required = false) String groupId,
                          @RequestParam(required = false, defaultValue = "10") String pageSize,
                          @RequestParam(required = false, defaultValue = "1") String pageNo) {
        Page<OpsMetricBasic> basicPage = new Page<>(Integer.parseInt(pageNo), Integer.parseInt(pageSize));
        LambdaQueryWrapper<OpsMetricBasic> basicWrapper = Wrappers.lambdaQuery();
        basicWrapper.like(StrUtil.isNotEmpty(name), OpsMetricBasic::getMetricName, name);
        basicWrapper.eq(StrUtil.isNotEmpty(groupId), OpsMetricBasic::getGroupId, groupId);
        basicWrapper.orderByAsc(OpsMetricBasic::getOrderSort);
        Page<OpsMetricBasic> page = metricBasicService.page(basicPage, basicWrapper);
        if(page.getRecords().isEmpty()){
            return R.data(customPage(basicPage, i->i));
        }
        Map<String, String> nameMap = systemFeignService.idNameMapper();
        Map<String, List<OpsMetricTag>> tagMap = metricBasicService.findTagsByMetricId(page.getRecords().stream().map(i -> i.getId().toString()).collect(Collectors.toList()));
        return R.data(customPage(page, i -> OpsMetricBasicVO.fromEntity(i, tagMap, nameMap)));
    }

    @ControllerAuditLog(value = "指标基本信息-详情", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/getById")
    public R<Object> getById(@RequestParam("id") String id) {
        return R.data(metricBasicService.getById(id));
    }

    @ControllerAuditLog(value = "指标基本信息-新增", operateType = OperateType.INSERT, moduleName = ModuleName.INDICATOR)
    @PostMapping("/save")
    public R<Object> save(@RequestBody OpsMetricBasicVO metricBasic) {
        metricBasicService.saveOrUpdateOp(metricBasic);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    @ControllerAuditLog(value = "指标基本信息-另存为", operateType = OperateType.INSERT, moduleName = ModuleName.INDICATOR)
    @GetMapping("/saveAs")
    public R<Object> saveAs(@RequestParam("id") String id) {
        metricBasicService.saveAsById(id);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    @ControllerAuditLog(value = "指标基本信息-删除", operateType = OperateType.DELETE, moduleName = ModuleName.INDICATOR)
    @GetMapping("/delete")
    public R<Object> delete(@RequestParam("id") String id) {
        //todo 指标中心引用后，验证
        metricBasicService.deleted(id);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }


    @ControllerAuditLog(value = "指标基本信息-新增（feign）", operateType = OperateType.INSERT, moduleName = ModuleName.INDICATOR)
    @PostMapping("/api/save")
    public R<Object> apiConf(@RequestBody OpsMetricBasicApiVO metricBasic) {
        metricBasicService.saveOrUpdateApiConf(metricBasic);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    @ControllerAuditLog(value = "指标基本信息-详情", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/api/detail")
    public R<Object> apiDetail(@RequestParam("metricId") String metricId) {
        OpsMetricApiConfig apiConfig = metricBasicService.apiConfByMetricId(metricId);
        if (Objects.isNull(apiConfig)) {
            return R.data(null);
        }
        OpsMetricApiConfigVO apiConfigVO = new OpsMetricApiConfigVO();
        CopyOptions copyOptions = CopyOptions.create().setIgnoreProperties("params");
        BeanUtil.copyProperties(apiConfig, apiConfigVO, copyOptions);
        apiConfigVO.setParams(JsonUtils.toMap(apiConfig.getParams()));
        return R.data(apiConfigVO);
    }

    /**
     * openfeign 调用
     *
     * @param flowId 工作流id
     * @return 关系数量
     */
    @ControllerAuditLog(value = "指标基本信息-查询关联的工作流", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/metric-relation")
    public R<Object> getMetricRelation(@RequestParam("flowId") String flowId) {
        long count = metricBasicService.count(Wrappers.lambdaQuery(OpsMetricBasic.class).eq(OpsMetricBasic::getTaskFlowId, flowId));
        return R.data(count);
    }

    /**
     * openfeign 调用
     *
     * @param permId 权限id
     * @return 关系数量
     */
    @ControllerAuditLog(value = "指标基本信息-查询关联权限", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/metric-perm")
    public R<Object> getMetricRelationForPerm(@RequestParam("permId") String permId) {
        long count = metricBasicService.countPerm(permId);
        return R.data(count);
    }

    @ControllerAuditLog(value = "指标数据结果配置-保存配置", operateType = OperateType.INSERT, moduleName = ModuleName.INDICATOR)
    @PostMapping("/field/save")
    public R<Object> saveField(@RequestBody OpsMetricFieldVO fieldVO) {
        metricBasicService.saveOrUpdateBasicResultConfig(fieldVO);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    @ControllerAuditLog(value = "指标数据结果配置-查询配置", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/field/detail")
    public R<Object> fieldDetail(@RequestParam("metricId") String metricId) {
        return R.data(metricBasicService.fieldDetailByMeticId(metricId));
    }

    @ControllerAuditLog(value = "指标基本信息-列表查（feign）", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/list")
    public R<Object> list() {
        List<OpsMetricBasic> list = metricBasicService.list(Wrappers.lambdaQuery(OpsMetricBasic.class).select(OpsMetricBasic::getId, OpsMetricBasic::getMetricName));
        return R.data(list);
    }

    @ControllerAuditLog(value = "指标基本信息-列表查字段（feign）", operateType = OperateType.QUERY, moduleName = ModuleName.INDICATOR)
    @GetMapping("/listFields")
    public R<List<OpsMetricBasicFieldVO>> listFields() {
        return R.data(metricBasicService.queryAllFields());
    }
}
