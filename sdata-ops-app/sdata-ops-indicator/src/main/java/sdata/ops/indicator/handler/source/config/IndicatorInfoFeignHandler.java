package sdata.ops.indicator.handler.source.config;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import sdata.ops.base.indicator.model.dto.IndicatorInfoDTO;
import sdata.ops.base.indicator.model.entity.IndicatorInfo;
import sdata.ops.base.indicator.model.entity.OpsMetricBasic;
import sdata.ops.base.indicator.model.entity.OpsMetricResultConfig;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.config.feign.FeignLocal;
import sdata.ops.indicator.api.feign.IndicatorInfoFeign;
import sdata.ops.indicator.service.IndicatorInfoService;
import sdata.ops.indicator.service.OpsMetricBasicService;
import sdata.ops.indicator.service.OpsMetricResultConfigService;

@Service
@FeignLocal(IndicatorInfoFeign.class)
@RequiredArgsConstructor
public class IndicatorInfoFeignHandler implements IndicatorInfoFeign {

    private final IndicatorInfoService infoService;

    private final OpsMetricBasicService metricBasicService;

    private final OpsMetricResultConfigService resultConfigService;


    /***
     * feign实现进程内调用support
     * @param dto 请求内容
     * @return R<Object>
     */
    @Override
    public R<Object> rpc(IndicatorInfoDTO dto) {
        String id = dto.getId();
        IndicatorInfo info = infoService.getById(id);
        if (null == info || StrUtil.isEmptyIfStr(info.getType())) {
            return R.fail(MessageConstant.PARAM_MISS);
        }
        BeanUtil.copyProperties(info, dto);
        return infoService.executeUp(dto);
    }

    @Override
    public R<Object> onceExeGenSpecTask(String replicaId) {
        return R.success("执行完成");
    }

    @Override
    public R<Object> getMetricRelation(String flowId) {
        long res = metricBasicService.count(Wrappers.lambdaQuery(OpsMetricBasic.class).eq(OpsMetricBasic::getTaskFlowId, flowId));
        return R.data(res);
    }

    @Override
    public R<Object> getMetricRelationForPerm(String permId) {
        long res = resultConfigService.count(Wrappers.lambdaQuery(OpsMetricResultConfig.class).eq(OpsMetricResultConfig::getPermissionId, permId));
        return R.data(res);
    }

}
