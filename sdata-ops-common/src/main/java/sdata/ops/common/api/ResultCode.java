package sdata.ops.common.api;

import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.servlet.http.HttpServletResponse;

import static javax.servlet.http.HttpServletResponse.*;

/**
 * <AUTHOR> ZJF
 * @description HTTP状态码
 * @date 2022/9/1
 */
@Getter
@AllArgsConstructor
public enum ResultCode implements IResultCode {

    /**
     * 操作成功
     */
    SUCCESS(HttpServletResponse.SC_OK, "操作成功"),

    /**
     * 业务异常
     */
    FAILURE(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "业务异常"),

    /**
     * 请求未授权
     */
    UN_AUTHORIZED(HttpServletResponse.SC_UNAUTHORIZED, "请求未授权"),

    /**
     * 404 没找到请求
     */
    NOT_FOUND(SC_NOT_FOUND, "404 没找到请求"),

    /**
     * 消息不能读取
     */
    MSG_NOT_READABLE(HttpServletResponse.SC_BAD_REQUEST, "消息不能读取"),

    /**
     * 不支持当前请求方法
     */
    METHOD_NOT_SUPPORTED(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "不支持当前请求方法"),

    /**
     * 不支持当前媒体类型
     */
    MEDIA_TYPE_NOT_SUPPORTED(HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE, "不支持当前媒体类型"),

    /**
     * 请求被拒绝
     */
    REQ_REJECT(SC_FORBIDDEN, "请求被拒绝"),

    /**
     * 服务器异常
     */
    INTERNAL_SERVER_ERROR(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "服务器异常"),

    /**
     * 缺少必要的请求参数
     */
    PARAM_MISS(HttpServletResponse.SC_BAD_REQUEST, "缺少必要的请求参数"),

    /**
     * 请求参数类型错误
     */
    PARAM_TYPE_ERROR(HttpServletResponse.SC_BAD_REQUEST, "请求参数类型错误"),

    /**
     * 请求参数绑定错误
     */
    PARAM_BIND_ERROR(HttpServletResponse.SC_BAD_REQUEST, "请求参数绑定错误"),

    /**
     * 参数校验失败
     */
    PARAM_VALID_ERROR(HttpServletResponse.SC_BAD_REQUEST, "参数校验失败"),
    /***
     * 流程相关异常
     ***/
    FLOW_MODEL_DESIGN_BPMN_FIRST(SC_BAD_REQUEST, "请先设计流程图"),
    FLOW_MODEL_BPMN_NOT_FOUND(SC_BAD_REQUEST, "流程设计不存在"),
    FLOW_MODEL_START_NODE_NOT_FOUND(SC_BAD_REQUEST, "开始节点不存在，请检查流程设计是否有误！"),
    FLOW_MODEL_FORM_NOT_FOUND(SC_BAD_REQUEST, "流程表单不存在"),
    FLOW_MODEL_ALREADY_LATEST(SC_BAD_REQUEST, "当前版本已是最新版"),
    FLOW_MODEL_NODE_CHECKER_NOT_FOUND(SC_BAD_REQUEST, "%s 节点未设置审批人！"),
    FLOW_MODEL_MOUNT_FORM_FIRST(SC_BAD_REQUEST, "请先配置流程表单"),
    FLOW_MODEL_NOT_FOUND(SC_NOT_FOUND, "流程模型不存在"),
    FLOW_MODEL_BPMN_ERROR(SC_NOT_FOUND, "获取模型设计失败"),
    FLOW_MODEL_BPMN_LOAD_ERROR(SC_INTERNAL_SERVER_ERROR, "加载xml文件异常"),

    FLOW_CATEGORY_EXIST(SC_BAD_REQUEST, "分类名或分类编码已存在, 分类编码:【%s】/分类名：【%s】"),
    FLOW_CATEGORY_USED(SC_FORBIDDEN, "该分类已被部署，无法删除: %s"),

    FLOW_PROCESS_ACTIVE_FIRST(SC_BAD_REQUEST, "流程已被挂起，请先激活流程"),
    FLOW_PROCESS_TASK_EMPTY(SC_BAD_REQUEST, "没有可办理的任务！"),
    FLOW_PROCESS_NOT_FOUND_SO_CANNOT_RETURN(SC_NOT_FOUND, "未找到对应的任务实例，无法执行退回操作"),
    FLOW_PROCESS_NOT_FOUND(SC_NOT_FOUND, "流程实例不存在，请确认！"),
    FLOW_PROCESS_CAN_NOT_DELETED_USED_PROCESS(SC_FORBIDDEN, "不允许删除进行中的流程实例"),
    FLOW_PROCESS_DONE_OR_NOT_STARTED(SC_FORBIDDEN, "流程未启动或已执行完成，取消申请失败"),
    FLOW_PROCESS_START_FAIL(SC_INTERNAL_SERVER_ERROR, "流程启动错误"),
    FLOW_PROCESS_NOT_FOUND_MAYBE_CHANGED(SC_INTERNAL_SERVER_ERROR, "未找到流程实例，流程可能已发生变化"),

    FLOW_TASK_COPY_FAIL(SC_BAD_REQUEST, "抄送任务失败"),
    FLOW_TASK_SUSPENDED(SC_BAD_REQUEST, "任务处于挂起状态"),
    FLOW_TASK_CANNOT_RETURN(SC_BAD_REQUEST, "当前节点相对于目标节点，不属于串行关系，无法回退"),
    FLOW_TASK_NOT_EXIST(SC_NOT_FOUND, "获取任务信息异常！"),
    FLOW_TASK_NOT_FOUND(SC_NOT_FOUND, "任务不存在"),
    FLOW_TASK_QUERY_FAIL(SC_NOT_FOUND, "获取任务失败！"),
    FLOW_TASK_CAN_RETURN_NODE_NOT_FOUND(SC_NOT_FOUND, "未找到可以退回的任务节点。当前运行任务: %s, 目标节点: %s。请确认目标节点是当前任务的前置节点且满足串行关系。"),
    FLOW_TASK_CANNOT_START_OR_END(SC_INTERNAL_SERVER_ERROR, "无法取消或开始活动："),
    FLOW_TASK_DONE_OR_SUSPEND(SC_FORBIDDEN, "流程已结束或已挂起，无法执行撤回操作"),
    FLOW_TASK_NEXT_IS_DONE(SC_FORBIDDEN, "下一流程已处理，无法执行撤回操作"),
    FLOW_TASK_NOT_EXIST_CANNOT_REVOKE(SC_NOT_FOUND, "当前任务不存在，无法执行撤回操作"),
    FLOW_TASK_MAYBE_CHANGED(SC_NOT_FOUND, "未找到流程实例，流程可能已发生变化"),
    FLOW_TASK_REINVOKE_FAIL(SC_INTERNAL_SERVER_ERROR, "执行撤回操作失败"),

    FLOW_FORM_QUERY_ERROR(SC_NOT_FOUND, "表单信息查询错误"),

    INDICATOR_JOB_TYPE_NOT_SUPPORT(SC_BAD_REQUEST, "暂不支持的调度任务类型!"),
    INDICATOR_JOB_TYPE_IS_NULL(SC_BAD_REQUEST, "任务类型错误"),
    ;
    /**
     * code编码
     */
    final int code;
    /**
     * 中文信息描述
     */
    final String message;

}
