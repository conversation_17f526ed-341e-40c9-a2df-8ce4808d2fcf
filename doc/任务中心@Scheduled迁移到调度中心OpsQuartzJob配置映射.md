# 任务中心@Scheduled迁移到调度中心OpsQuartzJob配置映射

## 概述

本文档详细说明如何将任务中心(sdata-ops-system)的7个@Scheduled定时任务转化为调度中心(sdata-ops-indicator)的OpsQuartzJob配置数据，实现配置化管理。

## OpsQuartzJob字段说明

基于 `sdata.ops.base.indicator.model.entity.OpsQuartzJob` 实体：

| 字段 | 类型 | 说明 | 示例值 |
|------|------|------|--------|
| jobName | String | 任务名称 | "每日任务清单生成" |
| jobGroup | String | 任务组名 | "TASK_CENTER_GROUP" |
| invokeTarget | String | 调用目标字符串 | "systemTaskBridge.creatTask()" |
| cronExpression | String | cron执行表达式 | "0 0 1 * * ?" |
| misfirePolicy | String | 错误策略(0默认/1立即执行/2执行一次/3放弃执行) | "0" |
| concurrent | String | 是否并发执行(0允许/1禁止) | "1" |
| status | String | 状态(0正常/1暂停) | "1" |
| jobType | String | 任务类型 | "dynamicTask" |
| market | String | 日历市场("0"不限/其他按交易日校验) | "0" |
| timeType | String | 时间类型(quick/custom) | "custom" |
| remark | String | 备注信息 | "原@Scheduled任务迁移" |

## 任务迁移配置映射

### 1. TaskJob.creatTask() - 每日任务清单生成

**原@Scheduled配置：**
```java
@Scheduled(cron = "0 0 1 * * ?")
@JobTaskDistributedLock(lockId = "1001")
public void creatTask()
```

**对应OpsQuartzJob配置：**
```json
{
  "jobName": "每日任务清单生成",
  "jobGroup": "TASK_CENTER_GROUP",
  "invokeTarget": "systemTaskBridge.creatTask()",
  "cronExpression": "0 0 1 * * ?",
  "misfirePolicy": "0",
  "concurrent": "1",
  "status": "1",
  "jobType": "dynamicTask",
  "market": "0",
  "timeType": "custom",
  "remark": "每日凌晨1点生成任务清单，含工作日检查和分布式锁1001"
}
```

### 2. TaskJob.obtThirdSystemDataForSch() - 三方系统数据抓取

**原@Scheduled配置：**
```java
@Scheduled(cron = "0 0 6-22 * * ?")
public void obtThirdSystemDataForSch()
```

**对应OpsQuartzJob配置：**
```json
{
  "jobName": "三方系统数据抓取",
  "jobGroup": "TASK_CENTER_GROUP", 
  "invokeTarget": "systemTaskBridge.obtThirdSystemDataForSch()",
  "cronExpression": "0 0 6-22 * * ?",
  "misfirePolicy": "0",
  "concurrent": "1",
  "status": "1",
  "jobType": "dynamicTask",
  "market": "0",
  "timeType": "custom",
  "remark": "每小时执行，抓取OA与邮件数据，含工作日检查"
}
```

### 3. TaskJob.updateTaskIsSearchForOaSystemOrMailServer() - 任务可见性更新

**原@Scheduled配置：**
```java
@Scheduled(cron = "0 0 6-22 * * ?")
public void updateTaskIsSearchForOaSystemOrMailServer()
```

**对应OpsQuartzJob配置：**
```json
{
  "jobName": "任务可见性轮询更新",
  "jobGroup": "TASK_CENTER_GROUP",
  "invokeTarget": "systemTaskBridge.updateTaskIsSearchForOaSystemOrMailServer()",
  "cronExpression": "0 0 6-22 * * ?", 
  "misfirePolicy": "0",
  "concurrent": "1",
  "status": "1",
  "jobType": "dynamicTask",
  "market": "0",
  "timeType": "custom",
  "remark": "轮询执行脚本更新任务可见性，含工作日检查"
}
```

### 4. TaskJob.insertTaskForOaSystemOrMailServer() - 增量任务创建

**原@Scheduled配置：**
```java
@Scheduled(cron = "0 0 6-22 * * ?")
@JobTaskDistributedLock(lockId = "1002")
public void insertTaskForOaSystemOrMailServer()
```

**对应OpsQuartzJob配置：**
```json
{
  "jobName": "增量任务创建",
  "jobGroup": "TASK_CENTER_GROUP",
  "invokeTarget": "systemTaskBridge.insertTaskForOaSystemOrMailServer()",
  "cronExpression": "0 0 6-22 * * ?",
  "misfirePolicy": "0", 
  "concurrent": "1",
  "status": "1",
  "jobType": "dynamicTask",
  "market": "0",
  "timeType": "custom",
  "remark": "基于OA/邮件命中增量创建任务，含工作日检查和分布式锁1002"
}
```

### 5. TaskJob.updateSpecDetailTask() - 特殊明细任务更新

**原@Scheduled配置：**
```java
@Scheduled(cron = "0 0 6-22 * * ?")
public void updateSpecDetailTask()
```

**对应OpsQuartzJob配置：**
```json
{
  "jobName": "特殊明细任务更新",
  "jobGroup": "TASK_CENTER_GROUP",
  "invokeTarget": "systemTaskBridge.updateSpecDetailTask()",
  "cronExpression": "0 0 6-22 * * ?",
  "misfirePolicy": "0",
  "concurrent": "1", 
  "status": "1",
  "jobType": "dynamicTask",
  "market": "0",
  "timeType": "custom",
  "remark": "特殊明细任务更新抓取数据写入明细表"
}
```

### 6. TaskJob.updateTaskOnlyOnce() - 特殊时间类型脚本

**原@Scheduled配置：**
```java
@Scheduled(cron = "0 0 2 * * ?")
public void updateTaskOnlyOnce()
```

**对应OpsQuartzJob配置：**
```json
{
  "jobName": "特殊时间类型脚本执行",
  "jobGroup": "TASK_CENTER_GROUP",
  "invokeTarget": "systemTaskBridge.updateTaskOnlyOnce()",
  "cronExpression": "0 0 2 * * ?",
  "misfirePolicy": "0",
  "concurrent": "1",
  "status": "1", 
  "jobType": "dynamicTask",
  "market": "0",
  "timeType": "custom",
  "remark": "每日凌晨2点执行一次，特殊时间周期类型脚本，含工作日检查"
}
```

### 7. OpsTaskGenInfoServiceImpl.timerEverDayScannerDelayTaskStatus() - 延期任务扫描

**原@Scheduled配置：**
```java
@Scheduled(cron = "0 0 13,15,17,22 * * ?")
public void timerEverDayScannerDelayTaskStatus()
```

**对应OpsQuartzJob配置：**
```json
{
  "jobName": "延期任务状态扫描",
  "jobGroup": "TASK_CENTER_GROUP",
  "invokeTarget": "systemTaskBridge.timerEverDayScannerDelayTaskStatus()",
  "cronExpression": "0 0 13,15,17,22 * * ?",
  "misfirePolicy": "0",
  "concurrent": "1",
  "status": "1",
  "jobType": "dynamicTask", 
  "market": "0",
  "timeType": "custom",
  "remark": "每天下午1点、3点、5点、10点扫描延期任务状态"
}
```

## 配置说明

### 通用配置原则

1. **jobGroup统一**: 所有任务使用 `"TASK_CENTER_GROUP"` 便于管理和区分
2. **jobType统一**: 使用 `"dynamicTask"` 类型，通过反射调用桥接Bean方法
3. **concurrent禁止**: 设置为 `"1"` 禁止并发，保持原有语义
4. **status暂停**: 初始设置为 `"1"` 暂停状态，待配置完成后手动启动
5. **market不限**: 设置为 `"0"` 不限制日历，工作日检查由业务逻辑内部处理
6. **timeType自定义**: 使用 `"custom"` 模式，直接使用原有cron表达式

### 分布式锁处理

- 原有的 `@JobTaskDistributedLock` 注解保持在业务方法上
- Quartz只负责触发，锁机制由系统服务内部实现
- 涉及任务：creatTask(锁1001)、insertTaskForOaSystemOrMailServer(锁1002)

### 工作日检查处理

- 原有的工作日检查逻辑保持在业务方法内部
- 所有任务的 `market` 字段设置为 `"0"` 不在Quartz层面限制
- 业务方法内部通过 `opsTradeTypeService.checkWorkdayByToday()` 进行检查

## 下一步实施计划

1. **创建桥接Bean**: 在调度中心创建 `SystemTaskBridge` 类
2. **扩展Feign接口**: 在 `SystemFeignService` 中添加对应方法
3. **配置数据导入**: 将上述7个配置导入到 `ops_quartz_job` 表
4. **测试验证**: 逐个启动任务进行功能验证
5. **原任务禁用**: 验证无误后禁用原@Scheduled任务

