# 任务中心迁移到调度中心 - 完整实施指南

## 概述

本文档提供任务中心(sdata-ops-system)的7个@Scheduled定时任务迁移到调度中心(sdata-ops-indicator)的完整实施指南。迁移采用**配置化方式**，通过Feign调用和桥接Bean实现，不迁移业务代码逻辑。

## 迁移架构

```
调度中心(Quartz) -> SystemTaskBridge -> SystemFeignService -> SystemFeignLocalHandler -> TaskJob/OpsTaskGenInfoService
```

### 核心组件

1. **OpsQuartzJob配置**: 7个任务的Quartz配置数据
2. **SystemTaskBridge**: 调度中心的桥接Bean，负责调用Feign接口
3. **SystemFeignService扩展**: 新增7个任务触发方法
4. **SystemFeignLocalHandler实现**: 本地代理，直接调用TaskJob方法

## 实施步骤

### 第一步：代码部署

#### 1.1 确认代码变更已部署

确保以下文件已正确部署：

- `sdata-ops-api/sdata-ops-system-api/src/main/java/sdata/ops/system/api/feign/SystemFeignService.java`
- `sdata-ops-app/sdata-ops-system/src/main/java/sdata/ops/system/handler/SystemFeignLocalHandler.java`
- `sdata-ops-app/sdata-ops-indicator/src/main/java/sdata/ops/indicator/handler/SystemTaskBridge.java`

#### 1.2 重启应用

重启sdata-ops-standalone应用，确保新增的Bean和接口生效。

### 第二步：配置数据导入

#### 2.1 执行SQL脚本

执行 `doc/任务中心迁移到调度中心-OpsQuartzJob配置数据.sql` 脚本：

```sql
-- 连接数据库
mysql -h [host] -u [username] -p [database_name]

-- 执行脚本
source doc/任务中心迁移到调度中心-OpsQuartzJob配置数据.sql
```

#### 2.2 验证数据导入

```sql
SELECT 
    id, job_name, job_group, invoke_target, cron_expression, 
    concurrent, status, job_type, market, time_type, remark
FROM ops_quartz_job 
WHERE job_group = 'TASK_CENTER_GROUP'
ORDER BY id;
```

应该看到7条记录，状态均为'1'(暂停)。

### 第三步：功能测试

#### 3.1 访问调度中心管理界面

访问：`http://localhost:8080/ops/indicator/scheduler/page`

#### 3.2 单任务测试

**建议测试顺序**：

1. **延期任务状态扫描** (最安全，只读操作)
2. **特殊明细任务更新** (相对安全)
3. **三方系统数据抓取** (含工作日检查)
4. **任务可见性轮询更新** (含工作日检查)
5. **特殊时间类型脚本执行** (含工作日检查)
6. **增量任务创建** (含分布式锁1002)
7. **每日任务清单生成** (含分布式锁1001，最重要)

**测试步骤**：
1. 在任务列表中找到要测试的任务
2. 点击"执行一次"按钮
3. 观察执行结果和日志
4. 确认无异常后，点击"启动"按钮启用定时执行

#### 3.3 日志检查

检查以下日志文件：
- 应用日志：确认SystemTaskBridge调用成功
- Quartz日志：确认任务调度正常
- 业务日志：确认原有业务逻辑执行正常

### 第四步：原任务禁用

#### 4.1 禁用@Scheduled任务

在确认调度中心任务正常运行后，禁用原@Scheduled任务：

**方法一：注释@EnableScheduling注解**
```java
// @EnableScheduling  // 注释此行
@Component
@Slf4j
public class TaskJob {
    // ...
}
```

**方法二：注释具体@Scheduled注解**
```java
// @Scheduled(cron = CREATE_TASK_CRON)
// @JobTaskDistributedLock(lockId = "1001")
public void creatTask() {
    // ...
}
```

#### 4.2 验证禁用效果

重启应用后，确认原@Scheduled任务不再执行，只有调度中心的任务在运行。

## 监控与维护

### 日常监控

1. **调度中心监控**: 通过 `/indicator/scheduler/page` 监控任务执行状态
2. **执行日志查看**: 点击任务的"日志"按钮查看执行历史
3. **异常告警**: 关注任务执行失败的告警信息

### 常见问题处理

#### 问题1：SystemTaskBridge Bean未找到

**现象**: 启动时报错 "No qualifying bean of type 'SystemTaskBridge'"

**解决**: 确认SystemTaskBridge类已正确放置在component scan路径下，重启应用。

#### 问题2：Feign调用失败

**现象**: 执行任务时报Feign调用异常

**解决**: 
1. 检查SystemFeignService接口方法是否正确添加
2. 检查SystemFeignLocalHandler是否正确实现
3. 确认@FeignLocal注解配置正确

#### 问题3：分布式锁冲突

**现象**: 任务执行时报分布式锁获取失败

**解决**: 
1. 确认原@Scheduled任务已完全禁用
2. 检查是否有多个实例同时运行
3. 调整任务执行时间避免冲突

#### 问题4：工作日检查失败

**现象**: 非工作日任务仍在执行或工作日任务未执行

**解决**: 
1. 检查OpsSysCalendar表数据是否正确
2. 确认工作日检查逻辑在业务方法内部正常执行
3. 验证market字段设置为"0"

## 回滚方案

### 紧急回滚

如果迁移后出现严重问题，可以快速回滚：

#### 1. 暂停调度中心任务

```sql
UPDATE ops_quartz_job 
SET status = '1' 
WHERE job_group = 'TASK_CENTER_GROUP';
```

#### 2. 恢复原@Scheduled任务

取消注释@EnableScheduling或@Scheduled注解，重启应用。

#### 3. 删除迁移数据（可选）

```sql
DELETE FROM ops_quartz_job WHERE job_group = 'TASK_CENTER_GROUP';
```

### 完整回滚

如果需要完全回滚到迁移前状态：

1. 执行紧急回滚步骤
2. 删除新增的代码文件：
   - SystemTaskBridge.java
   - SystemFeignService中新增的方法
   - SystemFeignLocalHandler中新增的方法
3. 重新部署应用

## 性能优化建议

### 1. 任务执行时间优化

- 避免多个任务在同一时间点执行
- 根据业务重要性调整任务优先级
- 考虑将高频任务(每小时执行)错开执行

### 2. 监控告警优化

- 为关键任务配置执行失败告警
- 设置任务执行超时告警
- 监控任务执行耗时趋势

### 3. 日志优化

- 调整SystemTaskBridge日志级别
- 定期清理Quartz执行日志
- 优化业务方法的日志输出

## 总结

本次迁移实现了以下目标：

1. ✅ **配置化管理**: 7个@Scheduled任务转为OpsQuartzJob配置
2. ✅ **语义保持**: 保持原有的工作日检查、分布式锁、执行频率
3. ✅ **架构解耦**: 通过Feign接口实现调度中心与任务中心的解耦
4. ✅ **运维友好**: 提供可视化的任务管理界面
5. ✅ **可回滚**: 提供完整的回滚方案

迁移后，所有定时任务将通过调度中心统一管理，提高了系统的可维护性和可观测性。
