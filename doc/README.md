# sdata-ops-backend 文档目录

本目录包含sdata-ops-backend项目的完整技术文档。

## 文档结构

### 核心模块文档

#### 任务中心 (Task Center)
- **[任务中心模块-功能与流程说明.md](./任务中心模块-功能与流程说明.md)** - 任务中心整体架构和业务流程
- **[TaskJob功能分析文档.md](./TaskJob功能分析文档.md)** - TaskJob类的详细功能分析

#### 调度中心 (Scheduler Center)  
- **[调度中心.md](./调度中心.md)** - 基于Quartz的调度中心完整技术文档

### 迁移文档

#### 任务中心到调度中心迁移
- **[任务中心迁移到调度中心-完整实施指南.md](./任务中心迁移到调度中心-完整实施指南.md)** - 完整的迁移实施指南
- **[任务中心@Scheduled迁移到调度中心OpsQuartzJob配置映射.md](./任务中心@Scheduled迁移到调度中心OpsQuartzJob配置映射.md)** - 详细的配置映射设计
- **[任务中心迁移到调度中心-OpsQuartzJob配置数据.sql](./任务中心迁移到调度中心-OpsQuartzJob配置数据.sql)** - 配置数据SQL脚本

## 快速导航

### 🚀 快速开始
如果你是新接手项目的开发者，建议按以下顺序阅读：
1. [任务中心模块-功能与流程说明.md](./任务中心模块-功能与流程说明.md) - 了解任务管理整体架构
2. [调度中心.md](./调度中心.md) - 了解Quartz调度实现
3. [TaskJob功能分析文档.md](./TaskJob功能分析文档.md) - 深入了解定时任务实现

### 🔄 迁移实施
如果你需要执行任务中心到调度中心的迁移：
1. [任务中心@Scheduled迁移到调度中心OpsQuartzJob配置映射.md](./任务中心@Scheduled迁移到调度中心OpsQuartzJob配置映射.md) - 了解迁移设计
2. [任务中心迁移到调度中心-完整实施指南.md](./任务中心迁移到调度中心-完整实施指南.md) - 执行迁移步骤
3. [任务中心迁移到调度中心-OpsQuartzJob配置数据.sql](./任务中心迁移到调度中心-OpsQuartzJob配置数据.sql) - 导入配置数据

### 🛠️ 开发维护
- **任务开发**: 参考调度中心文档的扩展指南部分
- **问题排查**: 参考完整实施指南的常见问题处理部分
- **性能优化**: 参考完整实施指南的性能优化建议部分

## 技术架构概览

### 任务管理双中心架构

```
┌─────────────────┐    ┌─────────────────┐
│   任务中心       │    │   调度中心       │
│ (Task Center)   │    │(Scheduler Center)│
├─────────────────┤    ├─────────────────┤
│ • 任务清单管理   │    │ • Quartz调度     │
│ • 业务流程编排   │    │ • 可视化管理     │
│ • 工作日检查     │    │ • 执行日志       │
│ • 分布式锁       │    │ • 监控告警       │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┘
              Feign调用 + 桥接Bean
```

### 核心组件

- **TaskJob**: 任务中心的核心调度类，包含7个@Scheduled定时任务
- **OpsQuartzJob**: 调度中心的任务配置实体
- **SystemTaskBridge**: 桥接Bean，连接调度中心和任务中心
- **SystemFeignService**: Feign接口，提供跨模块调用能力

## 版本历史

- **v1.0** (2024-12-19): 完成任务中心到调度中心迁移方案设计和实施
- **v0.9** (2024-12): 任务中心和调度中心独立运行阶段

## 贡献指南

### 文档更新
- 新增功能时，请同步更新相关文档
- 修改配置时，请更新配置映射文档
- 发现问题时，请更新常见问题处理部分

### 代码规范
- 遵循现有的命名规范和代码结构
- 新增任务时，请参考调度中心文档的扩展指南
- 修改核心逻辑时，请更新对应的功能分析文档

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issue: 在项目仓库提交Issue
- 技术讨论: 参与项目技术讨论会
- 文档反馈: 直接修改文档并提交PR

---

*最后更新: 2024-12-19*
