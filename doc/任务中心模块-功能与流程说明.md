# 任务中心模块（Task Center）功能与流程说明

## 概览

任务中心模块负责运维任务的全生命周期管理：配置（任务单元/模板）、日常/周期化生成、第三方数据驱动的显隐与增量创建、任务操作（完成/未发生/复核/转派/删除）、以及权限过滤与看板统计。模块由控制层（Controller）、服务层（Service/ServiceImpl）、调度任务（TaskJob）、以及MyBatis Mapper/SQL组成。

核心包位置：
- 控制器：sdata-ops-app/sdata-ops-system/src/main/java/sdata/ops/system/controller
- 服务：sdata-ops-app/sdata-ops-system/src/main/java/sdata/ops/system/service(及impl)
- 调度：sdata-ops-app/sdata-ops-system/src/main/java/sdata/ops/system/job/TaskJob.java
- Mapper XML：sdata-ops-app/sdata-ops-system/src/main/resources/mapper
- 核心实体：sdata-ops-base/sdata-ops-system-model/src/main/java/sdata/ops/base/system/model/entity

## 核心领域模型

- OpsTaskAttrBasic（任务单元，配置源）
- OpsTaskAttrBasicReplica（模板内任务单元副本）
- OpsTaskTemplate（任务模板）/ OpsTaskTemplateRelation（模板-副本关系）
- OpsTaskGenInfo（当天/周期 任务清单实体，具体执行对象）
- OpsTradeType（脚本/规则触发器，typeVal: 1 工作日轮询, 2 数据同步）
- OpsTaskJobRelation（任务单元/副本 与 调度作业关联，用于“日常生成”）
- 其他：OpsTaskGenInfoFile（附件）、OpsTaskTransferConf（转派配置）、OpsSysCalendar（工作日历）

## 任务单元：任务类型（taskType）与任务触发类型（taskTriggerType）

本节解释“任务单元”在生成/调度中的两类关键属性的含义与差异，以及它们如何影响 TaskJob 调度与任务清单（OpsTaskGenInfo）的行为。

### 1) 定义与取值
- 任务类型 taskType（OpsTaskGenInfo.taskType）
  - daily：日常任务（工作日周期性）
  - period：周期任务（如每周/每月/特殊规则）
  - temp：临时任务（一次性或临时生成）
- 任务触发类型 taskTriggerType（OpsTaskGenInfo.taskTriggerType）
  - manual：手动触发（立即生成，不走Quartz，不等待规则）
  - daily：按工作日触发（由 TaskJob 定时轮询/生成）
  - dynamic：自定义触发（绑定脚本/规则，由 TaskJob 或触发器按规则判定生成）

注：模板（OpsTaskTemplate）层面也有 schedulerType（manual/daily/dynamic），与 taskTriggerType 含义一致，但作用范围是“模板整体的生成策略”。

### 2) 行为差异与生效点
- 生成入口（OpsTaskGenInfoServiceImpl.createSingleTask）
  - manual：立即入库生成任务清单（save），不创建调度计划；同时将来源任务单元状态更新为“上线”。
  - 非 manual（daily/dynamic）：不立即入库，调用 saveSchedulerPlanByUnit 注册一个“定时/规则型生成计划”，到时或命中规则后再入库；同时更新来源任务单元“上线”。

- 时间处理（hourMergeDate）
  - manual：保留提交的任务时间（不强制合并到“当天”）。
  - 非 manual：会将“配置的时分秒”合并到“当天日期”再计算（便于工作日/规则驱动的当日生成）。

- 显隐策略（creatTask 一致性约定）
  - 日常任务（taskType=daily）默认“直接可见”。
  - 其他类型（period/temp）默认“隐藏”，待脚本命中后显示并补充 start/endTime、workAmount。

- 调度与规则对接
  - daily（工作日）：由 TaskJob.creatTask 在 01:00 统一生成日常任务；06-22点按小时轮询脚本（typeVal=1）更新可见性与时间；必要时做增量生成。
  - dynamic（自定义）：绑定 OpsTradeType（taskTriggerId），由 TaskJob 的“自定义路径”或触发器立即执行（schedulerProcessByDynamic）；也可在 06-22点“数据型脚本”（typeVal=2）驱动增量生成（createType=1 的模板副本）。
  - period（周期）：多用于“特殊周期脚本”的任务；每日 02:00 的 updateTaskOnlyOnce 进行一次性启用/更新；也可结合规则判断是否在当期生成。

- 去重与幂等
  - 动态/周期型生成通常需要在生成前检查“当期是否已存在”，避免重复（实现位置：服务层在生成路径内判断，日志中可见“根据任务类型判定是否已经生成过该任务”）。

### 3) 典型组合与适用场景
- taskType=daily + taskTriggerType=daily：标准工作日任务。01:00 统一生成，当天轮询脚本命中后可见并补充时间/工作量。
- taskType=period + taskTriggerType=dynamic：特殊时间周期（如“每月第N个工作日”、“节前/节后”），由脚本判定命中后生成。
- taskType=temp + taskTriggerType=manual：临时/应急类任务，创建即入库，不依赖脚本和调度。

### 4) 配置要点（任务单元/模板）
- 任务单元（OpsTaskAttrBasic / Replica）
  - taskTriggerId：绑定脚本/规则（自定义/工作日轮询/数据同步）；
  - taskCronVal：可选的时间表达式（多用于模板/规则计划）；
  - 归属、优先级、附件、稽核等属性在生成清单时一并转换（convertGenInfo）。
- 模板（OpsTaskTemplate）
  - schedulerType=manual/daily/dynamic：决定模板整体生成策略；
  - createType=1 的副本支持“工作日每小时增量生成”。

### 5) 常见问题与建议
- 不要将“taskType=临时”与“taskTriggerType=daily”混用：临时任务建议 manual 立即入库，避免等待轮询。
- dynamic 触发务必配置 taskTriggerId（脚本/规则），否则不会生成。
- manual 触发下，尽量设置明确的开始/结束时间，避免 hourMergeDate 对当天时间的重置逻辑产生偏差。
- 日常任务默认可见，其余默认隐藏；是否显示、时间/工作量赋值依赖脚本命中，请确保脚本在指标服务侧配置完整并联调通过。

## 主要控制器与能力

- TasksUnitController（/taskUnit）
  - listByPage/list/getById/save/copy/delete/changeStatus：任务单元配置的增删改查与上下线
- TasksTemplateController（/taskTemplate）
  - listByPage/list/detail/save/templateView/templateCopy/import/delete：模板管理、Excel导入、层级副本维护
  - createByTemplate 移至任务中心（见下）
- TasksCenterController（/taskCenter）
  - 任务生成：createByUnit/createByUnitId/createByTemp/createByTemplateId/createByTemplate
  - 任务查询：listByPage、tempDetail、getById
  - 任务操作：taskReset、taskComplete、taskNoExist、rewriteTaskDesc、editTaskEndTime、deleteTask
  - 审核/转派：auditList、taskAudit/batchTaskAudit、taskTransfer/batchTaskTransfer
  - 特殊数据：thirdList、assignTask（第三方明细分配）
- TradeTypeController（/tradeType）：触发器（规则/脚本）管理与校验

## 调度任务（TaskJob）与时间线

- 每日01:00 creatTask（工作日）
  - 清理当天旧记录（realDeleted）
  - 按“上线”的任务单元生成 OpsTaskGenInfo（非模板的update型，taskCreateType=0）
  - 默认“日常任务”直接可见，其余先隐藏（deleted=1）
  - 自动转派（autoTransfer）：根据当日有效的转派配置设置经办/复核归属、父子关系修正等
  - 批量保存
- 每日02:00 updateTaskOnlyOnce（工作日）
  - 处理“特殊时间周期”脚本的一次性启用更新
- 工作日 6-22点每小时：
  - obtThirdSystemDataForSch：触发“数据同步”规则（typeVal=2）
  - updateTaskIsSearchForOaSystemOrMailServer：执行“工作日轮询脚本”（typeVal=1），命中后：取消隐藏、可填充任务起止时间与工作量
  - insertTaskForOaSystemOrMailServer：对模板副本中 createType=1 的单元，根据脚本命中结果增量生成任务

说明：任务生成与显隐、时间/工作量赋值均由脚本规则（OpsTradeType → 指标服务）驱动。

## 服务层关键逻辑

### 1) OpsTaskGenInfoServiceImpl（任务清单）

- 生成：
  - createSingleTask(info[, taskId])：手动则立即入库；非手动则创建调度计划（saveSchedulerPlanByUnit/Template），并更新任务单元状态为上线
  - schedulerProcessByDaily()：读取 OpsTaskJobRelation 关系，分别生成“日常单元任务”和“模板副本任务”
  - replaceIdAndFillChildIdsAndSort(save)：对模板副本生成的清单进行“新ID重映射/父ID替换/依赖ID替换/根节点聚合子孙ID/同层统一楼层ID”
- 查询：
  - pageCustom/pageCustomAudit/listCustomAudit/findChild/…：结合 ConditionTaskDTO 做权限过滤（管理员/部门负责人/岗位/个人等）并分页、树化
- 操作：
  - taskComplete/taskNoExist：完成/未发生，支持“叶子/非叶子节点处理”、“附件必填校验”、“依赖校验”、“待复核（2）/已完成（3）
    - 批量完成会校验：所有子节点依赖是否越界且完成、附件是否齐全
    - correctParentStatus：沿父链纠偏父节点状态（包含未发生与已完成混合场景）
    - 异步写入 operationCompleteId/operationCheckId（记录操作者）
  - reviewTaskProcess/batchReviewTaskProcess：复核通过，支持对子树的复核；完成后纠偏父链状态
  - transferTask/batchTransferTask：经办/复核转派（按岗位/个人），可将非根任务 parentId 置0以确保新归属可见；并更新父任务的子任务ID集合
  - createTaskByTemplate(TemplateVO, up)：模板生成任务或注册日常计划（按模板 schedulerType）

### 2) OpsTaskTemplateServiceImpl（模板）

- saveOrUpdateAll：保存模板元数据与副本树（先清理旧副本与关系，再批量保存新副本与关系）
- copy：复制模板与副本树（重新生成ID、修正父子关系）
- templateImport：从Excel导入，构建副本树、生成模板与关系
- deleteTemplate / deleteTemplateForTransfer：删除模板及其副本关系，或重建部分关联

### 3) OpsTaskAttrBasicReplicaServiceImpl（模板副本）

- viewList/queryListByTemplateId…：按模板查询副本（树/扁平）
- queryUnProcessDataByUserId：第三方数据“未处理”查询（辅助明细任务分配）
- modifySpecTaskInfo：标记第三方数据已处理（ops_script_temp）、写入任务明细（示例：基金信息 OpsTaskFundInfo）

### 4) OpsTradeTypeServiceImpl（脚本/规则）

- checkDateType(id)：调用指标服务（IndicatorInfoFeign.rpc），返回 TaskScriptResultVO（status、startTime/endTime/count等）
- batchCheckDateType(ids)：并发执行多规则；支持RequestAttributes上下文传递
- checkWorkdayByToday/工作日偏移工具：用于调度与周期时间计算

## 权限模型（ConditionTaskDTO）

服务层查询统一通过 ConditionTaskDTO 过滤：
- type=4（推测：超管/全量）
- type=3（组织维度）
- type=2（部门负责人：postIds）
- type=1（普通用户：个人 + 所在岗位 postIds）
筛选逻辑体现在 Mapper 的 pageCustom/findChild 等 SQL 片段中，对 owner_org_id、task_owner_type/Id、access_level 等进行约束。

## 关键工作流程

1) 任务配置（任务单元/模板）
- 配置任务单元（/taskUnit/save），支持复制、上下线
- 通过模板维护副本树并保存（/taskTemplate/save），支持复制/导入

2) 任务生成
- 日常：01:00 TaskJob.creatTask + schedulerProcessByDaily（模板/单元）
- 自定义：schedulerProcessByDynamic（按触发器立即生成）
- 增量：工作日每小时 insertTaskForOaSystemOrMailServer（createType=1）

3) 任务显隐与属性赋值
- 脚本轮询：updateTaskIsSearchForOaSystemOrMailServer → 命中后取消隐藏、填充 start/endTime、workAmount

4) 任务操作
- 完成/未发生：校验附件、依赖；状态流转到2（待复核）或3（已完成），并纠偏父链
- 复核：taskAudit/batchTaskAudit → 记录 operationCheckId，纠偏父链
- 转派：taskTransfer/batchTaskTransfer → 更新经办/复核归属与父子结构
- 编辑：rewriteTaskDesc 同步到来源单元/副本；editTaskEndTime；deleteTask（含子）

5) 第三方数据联动
- obtThirdSystemDataForSch：脚本触发数据同步
- thirdList/assignTask：查询“未处理数据”并按明细任务分配，写入标记与业务明细表

## 一天的时序图（文字版）
- 01:00：清理当天旧清单 → 生成“上线单元”任务 → 自动转派 → 入库（默认隐藏非“日常”）
- 06:00-22:00（每小时）：
  - 轮询脚本：命中则取消隐藏并填充时间/工作量
  - 数据同步：执行数据型脚本
  - 增量任务：对 createType=1 的副本执行增量生成
- 02:00：特殊周期脚本一次性更新
- 全天：用户在任务中心进行查询、完成/未发生、复核、转派、编辑、删除

## 数据与树关系要点
- 模板生成任务：replaceIdAndFillChildIdsAndSort 会
  - 重映射所有清单ID与父ID
  - 为根节点聚合所有子孙ID至 taskChildIds
  - 为同层节点分配统一 floorId，便于同层分组
- 父链纠偏：correctParentStatus/ correctCheck 沿父链计算合并状态

## 错误处理与幂等
- 通过工作日判断与分布式锁（在 TaskJob 中）避免重复生成
- 任务生成/转派/复核使用事务，批量更新与关系纠偏在一个事务内完成
- 脚本执行并发时保留 RequestAttributes，异常时兜底默认结果，记录日志

## 扩展与最佳实践
- 新增脚本：在 OpsTradeType 维护 indicatorId，与指标服务对接；在 TaskJob/Service 中按 typeVal 合理接入
- 新增任务属性：扩展 OpsTaskGenInfo 与相关转换逻辑（convertGenInfo）
- 大规模模板：优先使用副本树 + replaceIdAndFillChildIdsAndSort，减少运行时树计算
- 权限过滤：统一使用 ConditionTaskDTO，避免在 Controller 层散落判断

## 参考文件
- Controllers：TasksCenterController、TasksTemplateController、TasksUnitController、TradeTypeController
- Services：OpsTaskGenInfoServiceImpl、OpsTaskTemplateServiceImpl、OpsTaskAttrBasicReplicaServiceImpl、OpsTradeTypeServiceImpl
- Schedulers：TaskJob
- Entities：OpsTaskGenInfo、OpsTaskAttrBasic/Replica、OpsTaskTemplate、OpsTradeType、OpsTaskJobRelation
- Mappers：OpsTaskGenInfoMapper.xml、OpsTaskAttrBasicReplicaMapper.xml

—— 文档版本 1.0
