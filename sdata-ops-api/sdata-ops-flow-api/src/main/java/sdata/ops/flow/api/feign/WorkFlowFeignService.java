package sdata.ops.flow.api.feign;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sdata.ops.base.flow.model.entity.OpsAiWorkflow;
import sdata.ops.base.flow.model.vo.FlowTestVO;
import sdata.ops.base.flow.model.vo.OpsAiWorkflowVO;

@FeignClient(contextId = "sdataflowTestFeignService",name = "sdata-flow-engine", url = "${micro-service.local-switch-url}")
public interface WorkFlowFeignService {


    @GetMapping("/workflow/getByForFeign")
    OpsAiWorkflow getFlowById(@RequestParam("id")String id);

}
