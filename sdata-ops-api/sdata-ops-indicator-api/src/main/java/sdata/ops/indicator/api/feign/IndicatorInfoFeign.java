package sdata.ops.indicator.api.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import sdata.ops.base.indicator.model.dto.IndicatorInfoDTO;
import sdata.ops.common.api.R;

@FeignClient(url = "${micro-service.local-switch-url}",contextId = "indicatorInfoFeign",name = "sdata-indicator")
public interface IndicatorInfoFeign {



    @PostMapping("/indicator/rpc")
    R<Object>  rpc(@RequestBody IndicatorInfoDTO dto);

    @GetMapping("/xxxxx")
    R<Object>  onceExeGenSpecTask(String replicaId);


    @GetMapping("/metric-basic/metric-relation")
    R<Object>  getMetricRelation(String flowId);


    @GetMapping("/metric-basic/metric-perm")
    R<Object>  getMetricRelationForPerm(String permId);
}
