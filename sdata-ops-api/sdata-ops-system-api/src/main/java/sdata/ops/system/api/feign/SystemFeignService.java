package sdata.ops.system.api.feign;

import cn.hutool.json.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import sdata.ops.base.system.model.dto.OpsTaskInfoUpdateDTO;
import sdata.ops.base.system.model.entity.*;
import sdata.ops.base.system.model.vo.SystemTestVO;
import sdata.ops.base.system.model.vo.WorkDayVO;
import sdata.ops.common.api.R;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@FeignClient(contextId = "systemTestService", name = "sdata-system", url = "${micro-service.local-switch-url}")
public interface SystemFeignService {


    @GetMapping("/system/b")
    SystemTestVO getTestVo();

    @GetMapping("/system/c")
    SystemTestVO getTransVo();


    @GetMapping("/userController/idNameMapper")
    Map<String, String> idNameMapper();

    @PostMapping("/system/fund/saveByIndicator")
    R<String> saveFundInfo(@RequestBody OpsTaskInfoUpdateDTO dto);

    @GetMapping("/system/fund/getId")
    List<OpsTaskFundInfo> queryFundInfos(String replicaId);


    @GetMapping("/tradeType/workCheck")
    R<Boolean> checkTodayIsWorkDay(@RequestParam(value = "date", required = false) String date);

    @GetMapping("/xxx")
    R<String> getLastWorkDay(String date);

    @GetMapping("/xxxx")
    R<Boolean> deleteTaImportDate(String date);

    @PostMapping("/system/fundSave")
    R<String> saveAllFund(@RequestBody List<OpsTaskFundInfo> arr);

    @GetMapping("/xxxxf")
    R<Boolean> deleteFundInfoImportByDateAndDataId(@RequestParam("date") String date,@RequestParam("dataId") String dataId);

    @GetMapping("/xd")
    R<String> getNextWorkDay(String today);

    @GetMapping("/getWorkDay")
    String getWorkDay(@RequestBody WorkDayVO vo);


    @GetMapping("/system/dataPerm/getById")
    R<Object> getById(@RequestParam("id") String id);

    @GetMapping("/system/dataPerm/getDataPermById")
    JSONObject getDataPermById(@RequestParam("id") String id, @RequestParam("userId") String userId);

    @GetMapping("/system/system-data-dict/listByTypes")
    R<List<OpsSysDictItem>> dictListByTypes(@RequestParam List<String> dictType);

    @GetMapping("/userController/selectDeptById")
    String selectDeptById(@RequestParam Long deptId);

    @GetMapping("/userController/selectRoleById")
    String selectRoleById(@RequestParam Long roleId);

    @GetMapping("/userController/selectNickNameById")
    String selectNickNameById(@RequestParam Long userId);

    /**
     * 查找同部门的所有用户id
     *
     * @param userId 当前人的用户id
     * @return
     */
    @GetMapping("/userController/findSameDeptUserIds")
    List<String> findSameDeptUserIds(@RequestParam String userId);

    /**
     * 根据deptId查找下级部门的人员
     *
     * @param deptId 部门id
     * @return 本部门及下级部门有人
     */
    @GetMapping("/userController/findAllByPerUser")
    List<SystemUser> findAllByPerUser(@RequestParam String deptId);

    /**
     * 获取指定人的所有角色
     * @param userId 用户id
     * @return 用户的所有角色信息
     */
    List<OpsSysRole> selectMyRoleList(String userId);

    /**
     * 获取指定人的所有组织
     * @param userId 用户id
     * @return 指定人的所有组织信息
     */
    List<OpsSysOrg> selectMyOrgList(String userId);

    List<OpsSysOrg> selectChildrenDeptById(String dept);

    /**
     * 判定指定日期在指定市场下是否为交易日
     *
     * @param date   日期
     * @param market 市场类别
     * @return 是交易日返回true,否则false
     */
    @GetMapping("/calendar/isTradeDay")
    Boolean isTradeDay(@RequestParam LocalDate date, @RequestParam String market);

    // ========== 任务中心@Scheduled任务迁移到调度中心的触发方法 ==========

    /**
     * 每日任务清单生成
     * 原@Scheduled(cron = "0 0 1 * * ?")
     * 含分布式锁1001和工作日检查
     */
    @GetMapping("/taskCenter/creatTask")
    R<String> triggerCreatTask();

    /**
     * 三方系统数据抓取
     * 原@Scheduled(cron = "0 0 6-22 * * ?")
     * 含工作日检查
     */
    @GetMapping("/taskCenter/obtThirdSystemDataForSch")
    R<String> triggerObtThirdSystemDataForSch();

    /**
     * 任务可见性轮询更新
     * 原@Scheduled(cron = "0 0 6-22 * * ?")
     * 含工作日检查
     */
    @GetMapping("/taskCenter/updateTaskIsSearchForOaSystemOrMailServer")
    R<String> triggerUpdateTaskIsSearchForOaSystemOrMailServer();

    /**
     * 增量任务创建
     * 原@Scheduled(cron = "0 0 6-22 * * ?")
     * 含分布式锁1002和工作日检查
     */
    @GetMapping("/taskCenter/insertTaskForOaSystemOrMailServer")
    R<String> triggerInsertTaskForOaSystemOrMailServer();

    /**
     * 特殊明细任务更新
     * 原@Scheduled(cron = "0 0 6-22 * * ?")
     */
    @GetMapping("/taskCenter/updateSpecDetailTask")
    R<String> triggerUpdateSpecDetailTask();

    /**
     * 特殊时间类型脚本执行
     * 原@Scheduled(cron = "0 0 2 * * ?")
     * 含工作日检查
     */
    @GetMapping("/taskCenter/updateTaskOnlyOnce")
    R<String> triggerUpdateTaskOnlyOnce();

    /**
     * 延期任务状态扫描
     * 原@Scheduled(cron = "0 0 13,15,17,22 * * ?")
     */
    @GetMapping("/taskCenter/timerEverDayScannerDelayTaskStatus")
    R<String> triggerTimerEverDayScannerDelayTaskStatus();
}
