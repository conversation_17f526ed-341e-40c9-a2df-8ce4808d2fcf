# TaskJob 功能分析文档

## 概述

`TaskJob` 是一个基于Spring Boot的定时任务调度组件，位于 `sdata.ops.system.job` 包中。该类主要负责任务的自动化生成、更新、转派和数据同步等核心功能，是整个任务管理系统的核心调度器。

## 类基本信息

- **包路径**: `sdata.ops.system.job.TaskJob`
- **注解**: 
  - `@Component` - Spring组件
  - `@EnableScheduling` - 启用定时任务
  - `@RequiredArgsConstructor` - Lombok构造器注入
  - `@Slf4j` - 日志支持

## 核心功能模块

### 1. 每日任务生成模块

#### 1.1 主要任务生成 (`creatTask()`)
- **执行时间**: 每日凌晨1点 (`0 0 1 * * ?`)
- **分布式锁**: `@JobTaskDistributedLock(lockId = "1001")`
- **主要功能**:
  - 检查当日是否为工作日
  - 清理当日已存在的任务数据
  - 根据上线的任务单元生成当日任务清单
  - 处理任务的可见性（日常任务默认可见，其他类型需要脚本触发）
  - 执行自动转派逻辑
  - 批量保存生成的任务

#### 1.2 特殊时间类型任务生成
- **执行时间**: 每日凌晨2点 (`0 0 2 * * ?`)
- **功能**: 处理特殊时间周期类型的脚本任务

### 2. 自动转派模块 (`autoTransfer()`)

#### 2.1 转派配置查询
- 查询当日生效的转派配置
- 支持按时间范围过滤转派规则
- 处理转派的开始时间和结束时间逻辑

#### 2.2 转派类型处理
- **类型1**: 任务转派
  - `transferType = "1"`: 转派给组织
  - `transferType != "1"`: 转派给检查人员
- 支持设置转派描述、状态等属性
- 处理父子任务关系的调整

#### 2.3 父任务属性更新 (`updateParentTaskProperties()`)
- 更新父任务的子任务ID集合
- 剔除已转派的子任务ID
- 维护任务层级关系的一致性

### 3. 数据同步与更新模块

#### 3.1 三方系统数据抓取 (`obtThirdSystemDataForSch()`)
- **执行时间**: 工作日6-22点每小时执行 (`0 0 6-22 * * ?`)
- **功能**:
  - 抓取OA系统和邮件系统数据
  - 执行数据同步类型的规则
  - 更新相关任务状态

#### 3.2 任务可见性更新 (`updateTaskIsSearchForOaSystemOrMailServer()`)
- **执行时间**: 工作日6-22点每小时执行
- **功能**:
  - 根据自定义脚本结果更新任务可见性
  - 处理工作日轮询类型的脚本
  - 更新任务的开始时间、结束时间和工作量

#### 3.3 增量任务创建 (`insertTaskForOaSystemOrMailServer()`)
- **执行时间**: 工作日6-22点每小时执行
- **分布式锁**: `@JobTaskDistributedLock(lockId = "1002")`
- **功能**:
  - 基于OA或邮件系统命中结果增量创建任务
  - 处理任务模板副本数据
  - 执行脚本并根据结果创建新任务

#### 3.4 特殊明细任务更新 (`updateSpecDetailTask()`)
- **执行时间**: 工作日6-22点每小时执行
- **功能**:
  - 更新特殊明细任务的数据
  - 处理任务模板相关的明细信息
  - 执行相关脚本并记录结果

### 4. 特殊任务处理

#### 4.1 每日一次执行任务 (`updateTaskOnlyOnce()`)
- **执行时间**: 每日凌晨2点
- **功能**: 处理特殊时间周期类型的任务更新

#### 4.2 特殊任务明细生成 (`triggerSpecTaskDetailGen()`)
- 触发任务单元映射执行逻辑
- 调用指标服务生成特殊任务

## 依赖服务

### 核心服务依赖
- `OpsTaskGenInfoService`: 任务生成信息服务
- `OpsTaskAttrBasicService`: 任务基础属性服务
- `OpsTaskTemplateService`: 任务模板服务
- `OpsTaskAttrBasicReplicaService`: 任务属性副本服务
- `OpsTradeTypeService`: 交易类型服务
- `OpsTaskTransferConfMapper`: 任务转派配置映射器
- `IndicatorInfoFeign`: 指标信息远程调用服务

### 配置参数
- `third.sw.end`: 第三方系统开关配置

## 定时任务时间配置

| 任务类型 | Cron表达式 | 执行时间 | 说明 |
|---------|-----------|---------|------|
| 主要任务生成 | `0 0 1 * * ?` | 每日1点 | 生成当日任务清单 |
| 特殊任务生成 | `0 0 2 * * ?` | 每日2点 | 特殊时间类型任务 |
| 数据更新任务 | `0 0 6-22 * * ?` | 工作日6-22点每小时 | 多种更新任务 |

## 关键特性

### 1. 工作日检查
- 所有定时任务都会先检查当日是否为工作日
- 非工作日自动跳过执行

### 2. 分布式锁
- 关键任务使用 `@JobTaskDistributedLock` 确保集群环境下的唯一执行
- 防止重复执行和数据冲突

### 3. 批量处理
- 支持批量保存任务数据
- 优化数据库操作性能

### 4. 脚本驱动
- 任务的可见性和创建逻辑由脚本结果驱动
- 支持动态配置和灵活的业务规则

## 数据流转

1. **任务生成流程**:
   ```
   检查工作日 → 清理旧数据 → 查询任务单元 → 生成任务清单 → 处理可见性 → 自动转派 → 批量保存
   ```

2. **转派流程**:
   ```
   查询转派配置 → 匹配有效配置 → 应用转派规则 → 更新任务属性 → 处理父子关系
   ```

3. **数据同步流程**:
   ```
   检查工作日 → 执行脚本 → 获取结果 → 更新任务状态 → 记录日志
   ```

## 异常处理

- 完善的日志记录，便于问题追踪
- 工作日检查机制，避免非工作日执行
- 分布式锁机制，防止并发问题
- 批量操作的事务处理

## 扩展性

- 支持新的任务类型扩展
- 脚本驱动的业务规则，便于配置调整
- 模块化的服务依赖，便于功能扩展
- 灵活的转派配置机制

---

*文档版本: 1.0*  
*最后更新: 2024年*
